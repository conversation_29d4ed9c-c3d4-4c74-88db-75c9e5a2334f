<view class="followers-container data-v-2eeb3bc0"><scroll-view class="message-list data-v-2eeb3bc0" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['refresherrefresh',[['onRefresh',['$event']]]],['scrolltolower',[['loadMore',['$event']]]]]}}" bindrefresherrefresh="__e" bindscrolltolower="__e"><block wx:if="{{$root.g0}}"><view class="data-v-2eeb3bc0"><block wx:for="{{$root.l0}}" wx:for-item="message" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['openUserProfile',['$0'],[[['messageList','id',message.$orig.id,'userId']]]]]]]}}" class="message-card data-v-2eeb3bc0" bindtap="__e"><u-avatar class="user-avatar data-v-2eeb3bc0" vue-id="{{'28c262e6-1-'+__i0__}}" src="{{message.$orig.userAvatar}}" size="50" bind:__l="__l"></u-avatar><view class="user-info data-v-2eeb3bc0"><view class="user-header data-v-2eeb3bc0"><text class="user-name data-v-2eeb3bc0">{{message.$orig.userName}}</text><text class="follow-time data-v-2eeb3bc0">{{message.m0}}</text></view><text class="user-desc data-v-2eeb3bc0">{{message.$orig.userDesc||'这个人很懒，什么都没有留下'}}</text><block wx:if="{{message.g1}}"><view class="user-tags data-v-2eeb3bc0"><block wx:for="{{message.$orig.userTags}}" wx:for-item="tag" wx:for-index="__i1__" wx:key="*this"><u-tag class="tag-item data-v-2eeb3bc0" vue-id="{{'28c262e6-2-'+__i0__+'-'+__i1__}}" text="{{tag}}" size="mini" type="primary" mode="light" bind:__l="__l"></u-tag></block></view></block></view><view class="follow-actions data-v-2eeb3bc0"><follow-button vue-id="{{'28c262e6-3-'+__i0__}}" user="{{message.a0}}" followed="{{message.$orig.isFollowBack}}" size="mini" data-event-opts="{{[['^follow',[['onUserFollow']]],['^unfollow',[['onUserUnfollow']]],['^change',[['onFollowChange']]],['^click',[['',['$event']]]]]}}" bind:follow="__e" bind:unfollow="__e" bind:change="__e" catch:click="__e" class="data-v-2eeb3bc0" bind:__l="__l"></follow-button></view><block wx:if="{{!message.$orig.isRead}}"><view class="unread-dot data-v-2eeb3bc0"></view></block></view></block></view></block><block wx:else><view class="empty-state data-v-2eeb3bc0"><u-empty vue-id="28c262e6-4" mode="data" text="暂无新粉丝" class="data-v-2eeb3bc0" bind:__l="__l"></u-empty></view></block><block wx:if="{{$root.g2}}"><view class="load-more data-v-2eeb3bc0"><u-loading vue-id="28c262e6-5" mode="flower" size="24" class="data-v-2eeb3bc0" bind:__l="__l"></u-loading><text class="load-text data-v-2eeb3bc0">加载中...</text></view></block></scroll-view></view>