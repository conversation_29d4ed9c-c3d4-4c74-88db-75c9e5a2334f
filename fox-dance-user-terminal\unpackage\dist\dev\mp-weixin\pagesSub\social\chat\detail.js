(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesSub/social/chat/detail"],{

/***/ 766:
/*!********************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/main.js?{"page":"pagesSub%2Fsocial%2Fchat%2Fdetail"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./pagesSub/social/chat/detail.vue */ 767));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 767:
/*!***********************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=7a033bbb&scoped=true& */ 768);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 770);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& */ 772);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 73);

var renderjs





/* normalize component */

var component = Object(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "7a033bbb",
  null,
  false,
  _detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesSub/social/chat/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 768:
/*!******************************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=template&id=7a033bbb&scoped=true& ***!
  \******************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7a033bbb&scoped=true& */ 769);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_7a033bbb_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 769:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=template&id=7a033bbb&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uAvatar: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-avatar/u-avatar */ "components/uview-ui/components/u-avatar/u-avatar").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-avatar/u-avatar.vue */ 949))
    },
    uIcon: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-icon/u-icon */ "components/uview-ui/components/u-icon/u-icon").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-icon/u-icon.vue */ 818))
    },
    uLoading: function () {
      return __webpack_require__.e(/*! import() | components/uview-ui/components/u-loading/u-loading */ "components/uview-ui/components/u-loading/u-loading").then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-loading/u-loading.vue */ 935))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.messageList, function (message, __i0__) {
    var $orig = _vm.__get_orig(message)
    var m0 = message.showTime ? _vm.formatMessageTime(message.timestamp) : null
    return {
      $orig: $orig,
      m0: m0,
    }
  })
  var g0 = !_vm.voiceMode ? _vm.inputText && _vm.inputText.length > 400 : null
  var g1 = !_vm.voiceMode && g0 ? _vm.inputText.length : null
  var g2 = !_vm.voiceMode && g0 ? _vm.inputText.length : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g0: g0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 770:
/*!************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 771);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 771:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 83));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 85));
var _socialApi = __webpack_require__(/*! @/utils/socialApi.js */ 661);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  name: "ChatDetail",
  data: function data() {
    return {
      chatId: "",
      chatName: "",
      otherUserAvatar: "https://picsum.photos/100/100?random=800",
      isOnline: true,
      messageList: [],
      inputText: "",
      scrollTop: 0,
      showExtensions: false,
      showEmojis: false,
      voiceMode: false,
      isRecording: false,
      inputFocused: false,
      isSending: false,
      isTyping: false,
      typingTimer: null,
      currentUser: {
        avatar: ""
      },
      // 语音录制相关
      recorderManager: null,
      voiceRecordPath: "",
      voiceRecordDuration: 0,
      recordStartTime: 0,
      currentPlayingVoice: null,
      innerAudioContext: null,
      emojiList: ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚", "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", "🥳", "😏"],
      // uview输入框自定义样式
      inputCustomStyle: {
        backgroundColor: "transparent",
        fontSize: "32rpx",
        lineHeight: "1.4",
        minHeight: "40rpx",
        maxHeight: "200rpx",
        padding: "0",
        color: "#333"
      },
      // placeholder样式
      placeholderStyle: "color: #999; font-size: 32rpx;",
      // WebSocket相关
      socketTask: null,
      isConnected: false,
      reconnectTimer: null,
      reconnectCount: 0,
      maxReconnectCount: 5,
      heartbeatTimer: null
    };
  },
  onLoad: function onLoad(options) {
    console.log("聊天页面参数:", options);

    // 支持从用户主页跳转的参数
    if (options.userId) {
      this.chatId = options.userId;
      this.chatName = decodeURIComponent(options.nickname || "用户");
      this.otherUserAvatar = options.avatar ? decodeURIComponent(options.avatar) : "https://picsum.photos/100/100?random=800";
    } else {
      // 兼容原有的参数格式
      this.chatId = options.id;
      this.chatName = options.name || "聊天";
    }
    console.log("聊天对象信息:", {
      chatId: this.chatId,
      chatName: this.chatName,
      otherUserAvatar: this.otherUserAvatar
    });

    // 初始化语音录制管理器
    this.initRecorderManager();

    // 加载当前用户头像
    this.loadCurrentUserInfo();
    this.loadMessages();
    this.connectWebSocket();
  },
  computed: {
    canSendMessage: function canSendMessage() {
      return this.inputText.trim().length > 0 && this.inputText.length <= 500 && !this.isSending;
    }
  },
  onUnload: function onUnload() {
    this.disconnectWebSocket();
    this.cleanupAudio();
  },
  onHide: function onHide() {
    this.disconnectWebSocket();
    this.cleanupAudio();
  },
  onShow: function onShow() {
    if (!this.isConnected) {
      this.connectWebSocket();
    }
  },
  methods: {
    // ==================== WebSocket相关方法 ====================
    connectWebSocket: function connectWebSocket() {
      var _this = this;
      if (this.isConnected || this.socketTask) {
        return;
      }
      console.log("开始连接WebSocket...");

      // 获取当前用户ID和token
      var currentUserId = uni.getStorageSync("userid");
      var token = uni.getStorageSync("token");
      if (!currentUserId || !token) {
        console.error("缺少用户ID或token，无法连接WebSocket");
        return;
      }

      // 检查开发者工具设置
      console.log("📱 当前环境信息:");
      console.log("- 平台:", uni.getSystemInfoSync().platform);
      console.log("- 环境:", "development");

      // 构建WebSocket URL
      var wsUrl = "".concat(this.getWebSocketUrl(), "?userId=").concat(currentUserId, "&token=").concat(token);
      console.log("🔗 尝试连接WebSocket:", wsUrl);

      // 尝试不同的连接方式
      this.socketTask = uni.connectSocket({
        url: wsUrl,
        protocols: [],
        // 明确指定协议
        success: function success() {
          console.log("✅ WebSocket连接请求发送成功");
        },
        fail: function fail(error) {
          console.error("❌ WebSocket连接失败:", error);
          console.error("连接URL:", wsUrl);
          console.error("用户ID:", currentUserId);
          console.error("Token:", token);
          console.error("错误详情:", JSON.stringify(error));

          // 尝试使用HTTP测试连接性
          _this.testHttpConnection();

          // 显示用户友好的错误信息
          uni.showToast({
            title: "WebSocket连接失败",
            icon: "none",
            duration: 3000
          });
          _this.scheduleReconnect();
        }
      });

      // 监听WebSocket连接打开
      this.socketTask.onOpen(function () {
        console.log("🎉 WebSocket连接已打开");
        _this.isConnected = true;
        _this.reconnectCount = 0;
        _this.startHeartbeat();

        // 显示连接成功提示
        uni.showToast({
          title: "连接成功",
          icon: "success",
          duration: 2000
        });

        // 加入聊天房间
        _this.joinChatRoom();
      });

      // 监听WebSocket消息
      this.socketTask.onMessage(function (res) {
        console.log("收到WebSocket消息:", res.data);
        _this.handleWebSocketMessage(res.data);
      });

      // 监听WebSocket连接关闭
      this.socketTask.onClose(function () {
        console.log("WebSocket连接已关闭");
        _this.isConnected = false;
        _this.stopHeartbeat();
        _this.scheduleReconnect();
      });

      // 监听WebSocket错误
      this.socketTask.onError(function (error) {
        console.error("WebSocket错误:", error);
        _this.isConnected = false;
        _this.scheduleReconnect();
      });
    },
    // 模拟WebSocket连接（用于测试）
    simulateWebSocketConnection: function simulateWebSocketConnection() {
      var _this2 = this;
      console.log("🔧 模拟WebSocket连接成功");
      this.isConnected = true;
      this.reconnectCount = 0;

      // 模拟连接成功
      uni.showToast({
        title: "连接成功（模拟）",
        icon: "success",
        duration: 2000
      });

      // 模拟接收消息（用于测试）
      setTimeout(function () {
        _this2.simulateReceiveMessage();
      }, 3000);
    },
    // 模拟接收消息
    simulateReceiveMessage: function simulateReceiveMessage() {
      var mockMessage = {
        id: Date.now(),
        senderId: this.chatId,
        // 对方ID
        receiverId: uni.getStorageSync("userid"),
        messageType: 1,
        content: "这是一条模拟接收的消息，用于测试聊天界面功能！",
        createTime: new Date().toISOString(),
        isRead: false
      };
      this.handleNewMessage(mockMessage);
      console.log("📨 模拟接收到消息:", mockMessage);
    },
    // 模拟对方回复
    simulateReply: function simulateReply(originalMessage) {
      var replies = ["收到你的消息了！", "好的，我知道了", "谢谢你的分享", "这个想法不错", "我也是这么想的", "\u5173\u4E8E\"".concat(originalMessage, "\"\uFF0C\u6211\u89C9\u5F97\u5F88\u6709\u610F\u601D"), "让我想想...", "同意你的观点"];
      var randomReply = replies[Math.floor(Math.random() * replies.length)];
      var replyMessage = {
        id: Date.now(),
        senderId: this.chatId,
        // 对方ID
        receiverId: uni.getStorageSync("userid"),
        messageType: 1,
        content: randomReply,
        createTime: new Date().toISOString(),
        isRead: false
      };
      this.handleNewMessage(replyMessage);
      console.log("🤖 模拟对方回复:", randomReply);
    },
    // 测试HTTP连接
    testHttpConnection: function testHttpConnection() {
      var testUrl = "http://192.168.1.21:8101/api/";
      console.log("🔍 测试HTTP连接:", testUrl);
      uni.request({
        url: testUrl,
        method: "GET",
        success: function success(res) {
          console.log("✅ HTTP连接成功:", res);
        },
        fail: function fail(error) {
          console.error("❌ HTTP连接失败:", error);
        }
      });
    },
    disconnectWebSocket: function disconnectWebSocket() {
      console.log("断开WebSocket连接");
      this.isConnected = false;
      this.stopHeartbeat();
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      if (this.socketTask) {
        this.socketTask.close();
        this.socketTask = null;
      }
    },
    scheduleReconnect: function scheduleReconnect() {
      var _this3 = this;
      if (this.reconnectCount >= this.maxReconnectCount) {
        console.log("达到最大重连次数，停止重连");
        return;
      }
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      var delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒
      console.log("".concat(delay, "ms\u540E\u5C1D\u8BD5\u91CD\u8FDE (\u7B2C").concat(this.reconnectCount + 1, "\u6B21)"));
      this.reconnectTimer = setTimeout(function () {
        _this3.reconnectCount++;
        _this3.connectWebSocket();
      }, delay);
    },
    startHeartbeat: function startHeartbeat() {
      var _this4 = this;
      this.stopHeartbeat();
      this.heartbeatTimer = setInterval(function () {
        if (_this4.isConnected && _this4.socketTask) {
          _this4.socketTask.send({
            data: JSON.stringify({
              type: "heartbeat",
              timestamp: Date.now()
            })
          });
        }
      }, 30000); // 30秒心跳
    },
    stopHeartbeat: function stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    getWebSocketUrl: function getWebSocketUrl() {
      // 根据环境返回WebSocket URL
      var systemInfo = uni.getSystemInfoSync();

      // 检查是否为开发环境
      if (true) {
        // 开发环境使用本地WebSocket服务（端口8102）
        console.log("🔧 开发环境，使用本地WebSocket服务");
        return "ws://localhost:8101/api/ws/chat";
      } else {}
    },
    joinChatRoom: function joinChatRoom() {
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "join",
            chatId: this.chatId,
            userId: uni.getStorageSync("userid")
          })
        });
      }
    },
    handleWebSocketMessage: function handleWebSocketMessage(data) {
      try {
        var message = JSON.parse(data);
        console.log("处理WebSocket消息:", message);
        switch (message.type) {
          case "message":
            this.handleNewMessage(message.data);
            break;
          case "typing":
            this.handleTypingStatus(message.data);
            break;
          case "read":
            this.handleMessageRead(message.data);
            break;
          case "online":
            this.handleOnlineStatus(message.data);
            break;
          default:
            console.log("未知消息类型:", message.type);
        }
      } catch (error) {
        console.error("解析WebSocket消息失败:", error);
      }
    },
    handleNewMessage: function handleNewMessage(messageData) {
      // 只处理对方发送的消息
      if (messageData.senderId != this.chatId) {
        return;
      }
      var message = {
        id: messageData.id,
        type: this.getMessageTypeString(messageData.messageType),
        content: messageData.content,
        isMine: false,
        avatar: this.otherUserAvatar,
        timestamp: new Date(messageData.createTime),
        showTime: this.shouldShowTime(messageData.createTime),
        status: "sent",
        isPlaying: false
      };

      // 如果是语音消息，添加duration字段
      if (messageData.messageType === 3) {
        // 优先使用mediaDuration字段，否则从content中提取时长
        if (messageData.mediaDuration) {
          message.duration = messageData.mediaDuration;
        } else {
          var durationMatch = messageData.content.match(/(\d+)秒/);
          message.duration = durationMatch ? parseInt(durationMatch[1]) : 1;
        }
        // 如果有mediaUrl，使用mediaUrl作为播放地址
        if (messageData.mediaUrl) {
          message.content = messageData.mediaUrl;
        }
      }
      this.messageList.push(message);
      this.scrollToBottom();

      // 播放消息提示音
      this.playMessageSound();
    },
    handleTypingStatus: function handleTypingStatus(data) {
      // 处理对方正在输入状态
      console.log("对方正在输入:", data);
    },
    handleMessageRead: function handleMessageRead(data) {
      // 处理消息已读状态
      var messageIndex = this.messageList.findIndex(function (msg) {
        return msg.id === data.messageId;
      });
      if (messageIndex !== -1) {
        this.messageList[messageIndex].status = "read";
      }
    },
    handleOnlineStatus: function handleOnlineStatus(data) {
      // 处理在线状态
      this.isOnline = data.isOnline;
    },
    shouldShowTime: function shouldShowTime(timestamp) {
      if (this.messageList.length === 0) return true;
      var lastMessage = this.messageList[this.messageList.length - 1];
      var timeDiff = new Date(timestamp) - lastMessage.timestamp;
      return timeDiff > 300000; // 5分钟
    },
    playMessageSound: function playMessageSound() {
      // 播放消息提示音
      try {
        var innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.src = "/static/sounds/message.mp3";
        innerAudioContext.play();
      } catch (error) {
        console.log("播放提示音失败:", error);
      }
    },
    // ==================== 原有方法 ====================
    loadCurrentUserInfo: function loadCurrentUserInfo() {
      // 从缓存中获取当前用户信息
      var userInfo = uni.getStorageSync("user");
      if (userInfo && userInfo.data) {
        this.currentUser.avatar = userInfo.data.avatar ? "https://file.foxdance.com.cn" + userInfo.data.avatar : "/static/images/toux.png";
      } else {
        this.currentUser.avatar = "/static/images/toux.png";
      }
      console.log("当前用户头像:", this.currentUser.avatar);
    },
    loadMessages: function loadMessages() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var currentUserId, result, messages;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                console.log("开始加载聊天消息，对方用户ID:", _this5.chatId);
                _context.prev = 1;
                // 获取当前用户ID
                currentUserId = _this5.getCurrentUserId();
                if (currentUserId) {
                  _context.next = 7;
                  break;
                }
                console.error("当前用户ID无效，无法加载消息");
                uni.showToast({
                  title: "请先登录",
                  icon: "none"
                });
                return _context.abrupt("return");
              case 7:
                console.log("当前用户ID:", currentUserId, "对方用户ID:", _this5.chatId);
                _context.next = 10;
                return (0, _socialApi.getConversationMessages)(_this5.chatId, {
                  current: 1,
                  size: 50
                });
              case 10:
                result = _context.sent;
                console.log("聊天消息API返回:", result);

                // 处理API返回的数据格式
                messages = [];
                if (result && result.code === 0 && result.data && Array.isArray(result.data)) {
                  messages = result.data;
                } else if (result && Array.isArray(result)) {
                  messages = result;
                } else {
                  console.log("没有找到聊天消息或格式不正确");
                  messages = [];
                }
                if (!(messages.length > 0)) {
                  _context.next = 21;
                  break;
                }
                _this5.messageList = messages.map(function (message, index) {
                  var prevMessage = messages[index - 1];
                  var showTime = !prevMessage || new Date(message.createTime) - new Date(prevMessage.createTime) > 300000; // 5分钟

                  var messageObj = {
                    id: message.id,
                    type: _this5.getMessageTypeString(message.messageType),
                    content: message.content,
                    isMine: message.senderId === currentUserId,
                    avatar: message.senderId === currentUserId ? _this5.currentUser.avatar : _this5.otherUserAvatar,
                    timestamp: new Date(message.createTime),
                    showTime: showTime,
                    status: message.isRead === 1 ? "read" : "sent"
                  };

                  // 如果是语音消息，添加语音相关字段
                  if (message.messageType === 3) {
                    messageObj.isPlaying = false;
                    // 优先使用mediaDuration字段，否则从content中提取时长
                    if (message.mediaDuration) {
                      messageObj.duration = message.mediaDuration;
                    } else {
                      var durationMatch = message.content.match(/(\d+)秒/);
                      messageObj.duration = durationMatch ? parseInt(durationMatch[1]) : 1;
                    }
                    // 如果有mediaUrl，使用mediaUrl作为播放地址
                    if (message.mediaUrl) {
                      messageObj.content = message.mediaUrl;
                    }
                  }
                  return messageObj;
                }).reverse(); // 消息按时间正序显示

                console.log("消息列表处理完成，消息数量:", _this5.messageList.length);

                // 标记消息已读
                _context.next = 19;
                return _this5.markAllMessagesAsRead();
              case 19:
                _context.next = 23;
                break;
              case 21:
                console.log("没有历史消息，显示空聊天界面");
                _this5.messageList = [];
              case 23:
                _context.next = 30;
                break;
              case 25:
                _context.prev = 25;
                _context.t0 = _context["catch"](1);
                console.error("加载消息失败:", _context.t0);
                uni.showToast({
                  title: "消息加载失败",
                  icon: "none"
                });
                _this5.messageList = [];
              case 30:
                // 滚动到底部
                _this5.$nextTick(function () {
                  _this5.scrollToBottom();
                });
              case 31:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[1, 25]]);
      }))();
    },
    // 获取当前用户ID
    getCurrentUserId: function getCurrentUserId() {
      var userId = uni.getStorageSync("userid");
      return userId ? Number(userId) : null;
    },
    // 将消息类型数字转换为字符串
    getMessageTypeString: function getMessageTypeString(messageType) {
      var typeMap = {
        1: "text",
        2: "image",
        3: "voice",
        4: "video"
      };
      return typeMap[messageType] || "text";
    },
    // 标记所有消息已读
    markAllMessagesAsRead: function markAllMessagesAsRead() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var currentUserId;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                currentUserId = _this6.getCurrentUserId();
                if (currentUserId) {
                  _context2.next = 4;
                  break;
                }
                return _context2.abrupt("return");
              case 4:
                _context2.next = 6;
                return (0, _socialApi.markMessageAsRead)({
                  senderId: _this6.chatId,
                  receiverId: currentUserId
                });
              case 6:
                console.log("消息已标记为已读");
                _context2.next = 12;
                break;
              case 9:
                _context2.prev = 9;
                _context2.t0 = _context2["catch"](0);
                console.error("标记消息已读失败:", _context2.t0);
              case 12:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 9]]);
      }))();
    },
    formatMessageTime: function formatMessageTime(timestamp) {
      var date = new Date(timestamp);
      var now = new Date();
      var diff = now - date;
      if (diff < 86400000) {
        // 今天
        return "".concat(date.getHours().toString().padStart(2, "0"), ":").concat(date.getMinutes().toString().padStart(2, "0"));
      } else {
        return "".concat(date.getMonth() + 1, "\u6708").concat(date.getDate(), "\u65E5 ").concat(date.getHours().toString().padStart(2, "0"), ":").concat(date.getMinutes().toString().padStart(2, "0"));
      }
    },
    scrollToBottom: function scrollToBottom() {
      var _this7 = this;
      this.$nextTick(function () {
        _this7.scrollTop = 999999;
      });
    },
    sendMessage: function sendMessage() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var messageContent, tempId, message, currentUserId, result;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (_this8.canSendMessage) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                messageContent = _this8.inputText.trim();
                tempId = Date.now(); // 设置发送状态
                _this8.isSending = true;

                // 停止输入状态提示
                _this8.stopTypingIndicator();
                message = {
                  id: tempId,
                  type: "text",
                  content: messageContent,
                  isMine: true,
                  timestamp: new Date(),
                  showTime: _this8.shouldShowTime(new Date()),
                  status: "sending"
                };
                _this8.messageList.push(message);
                _this8.inputText = "";
                _this8.scrollToBottom();
                _context3.prev = 10;
                // 获取当前用户ID
                currentUserId = uni.getStorageSync("userid"); // 1. 先通过API发送消息到服务器
                _context3.next = 14;
                return (0, _socialApi.sendMessage)({
                  receiverId: Number(_this8.chatId),
                  messageType: 1,
                  // 1表示文本消息
                  content: messageContent
                });
              case 14:
                result = _context3.sent;
                console.log("发送消息API返回:", result);
                if (result && result.code === 0) {
                  // 更新消息状态和ID
                  message.id = result.data.id || tempId;
                  message.status = "sent";

                  // 2. 通过WebSocket实时推送消息
                  if (_this8.isConnected && _this8.socketTask) {
                    _this8.socketTask.send({
                      data: JSON.stringify({
                        type: "message",
                        data: {
                          id: message.id,
                          senderId: currentUserId,
                          receiverId: _this8.chatId,
                          messageType: 1,
                          content: messageContent,
                          createTime: new Date().toISOString()
                        }
                      })
                    });
                    console.log("消息已通过WebSocket发送");
                  }
                  console.log("消息发送成功:", result.data);
                } else {
                  message.status = "failed";
                  console.error("消息发送失败:", result);
                  uni.showToast({
                    title: (result === null || result === void 0 ? void 0 : result.message) || "发送失败",
                    icon: "none"
                  });
                }
                _context3.next = 24;
                break;
              case 19:
                _context3.prev = 19;
                _context3.t0 = _context3["catch"](10);
                console.error("发送消息失败:", _context3.t0);
                message.status = "failed";
                uni.showToast({
                  title: "发送失败",
                  icon: "none"
                });
              case 24:
                _context3.prev = 24;
                // 重置发送状态
                _this8.isSending = false;
                return _context3.finish(24);
              case 27:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[10, 19, 24, 27]]);
      }))();
    },
    chooseImage: function chooseImage() {
      var _this9 = this;
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: function success(res) {
          var message = {
            id: Date.now(),
            type: "image",
            content: res.tempFilePaths[0],
            isMine: true,
            timestamp: new Date(),
            showTime: false,
            status: "sending"
          };
          _this9.messageList.push(message);
          _this9.showExtensions = false;
          _this9.scrollToBottom();

          // 模拟上传成功
          setTimeout(function () {
            message.status = "sent";
          }, 1000);
        }
      });
    },
    previewImage: function previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    startVoiceRecord: function startVoiceRecord() {
      this.voiceMode = true;
      this.showExtensions = false;
    },
    toggleVoiceMode: function toggleVoiceMode() {
      this.voiceMode = !this.voiceMode;
    },
    // ==================== 语音录制相关方法 ====================
    // 初始化录音管理器
    initRecorderManager: function initRecorderManager() {
      var _this10 = this;
      this.recorderManager = uni.getRecorderManager();

      // 录音开始事件
      this.recorderManager.onStart(function () {
        console.log("录音开始");
        _this10.recordStartTime = Date.now();
      });

      // 录音结束事件
      this.recorderManager.onStop(function (res) {
        console.log("录音结束", res);
        _this10.voiceRecordPath = res.tempFilePath;
        _this10.voiceRecordDuration = Math.floor(res.duration / 1000); // 转换为秒

        // 发送语音消息
        _this10.sendVoiceMessage(res.tempFilePath, _this10.voiceRecordDuration);
      });

      // 录音错误事件
      this.recorderManager.onError(function (err) {
        console.error("录音错误:", err);
        _this10.isRecording = false;
        uni.showToast({
          title: "录音失败",
          icon: "none"
        });
      });
    },
    startRecord: function startRecord() {
      var _this11 = this;
      console.log("开始录音");
      this.isRecording = true;

      // 检查录音权限
      uni.authorize({
        scope: "scope.record",
        success: function success() {
          // 开始录音
          _this11.recorderManager.start({
            duration: 60000,
            // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: "mp3",
            frameSize: 50
          });
        },
        fail: function fail() {
          _this11.isRecording = false;
          uni.showModal({
            title: "录音权限",
            content: "需要录音权限才能发送语音消息",
            confirmText: "去设置",
            success: function success(res) {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
    },
    stopRecord: function stopRecord() {
      if (!this.isRecording) return;
      console.log("停止录音");
      this.isRecording = false;

      // 检查录音时长
      var recordDuration = Date.now() - this.recordStartTime;
      if (recordDuration < 1000) {
        uni.showToast({
          title: "录音时间太短",
          icon: "none"
        });
        this.recorderManager.stop();
        return;
      }

      // 停止录音
      this.recorderManager.stop();
    },
    cancelRecord: function cancelRecord() {
      console.log("取消录音");
      this.isRecording = false;
      this.recorderManager.stop();
    },
    // 发送语音消息
    sendVoiceMessage: function sendVoiceMessage(tempFilePath, duration) {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var tempMessage, uploadResult, currentUserId, result;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                console.log("准备发送语音消息:", {
                  tempFilePath: tempFilePath,
                  duration: duration
                });

                // 创建临时消息显示在界面上
                tempMessage = {
                  id: Date.now(),
                  type: "voice",
                  content: "",
                  duration: duration,
                  isMine: true,
                  timestamp: new Date(),
                  showTime: _this12.shouldShowTime(new Date()),
                  status: "sending",
                  isPlaying: false
                };
                _this12.messageList.push(tempMessage);
                _this12.scrollToBottom();
                _context4.prev = 4;
                _context4.next = 7;
                return _this12.uploadVoiceFile(tempFilePath);
              case 7:
                uploadResult = _context4.sent;
                console.log("语音文件上传成功:", uploadResult);

                // 2. 发送消息到服务器
                currentUserId = uni.getStorageSync("userid");
                _context4.next = 12;
                return (0, _socialApi.sendMessage)({
                  receiverId: Number(_this12.chatId),
                  messageType: 3,
                  // 3表示语音消息
                  content: "\u8BED\u97F3\u6D88\u606F ".concat(duration, "\u79D2"),
                  mediaUrl: uploadResult.url,
                  mediaDuration: duration,
                  mediaSize: uploadResult.size || 0
                });
              case 12:
                result = _context4.sent;
                console.log("语音消息发送API返回:", result);
                if (result && result.code === 0) {
                  // 更新消息状态和ID
                  tempMessage.id = result.data.id || tempMessage.id;
                  tempMessage.status = "sent";
                  tempMessage.content = uploadResult.url; // 存储语音文件URL

                  // 通过WebSocket实时推送消息
                  if (_this12.isConnected && _this12.socketTask) {
                    _this12.socketTask.send({
                      data: JSON.stringify({
                        type: "message",
                        data: {
                          id: tempMessage.id,
                          senderId: currentUserId,
                          receiverId: _this12.chatId,
                          messageType: 3,
                          content: uploadResult.url,
                          mediaUrl: uploadResult.url,
                          mediaDuration: duration,
                          createTime: new Date().toISOString()
                        }
                      })
                    });
                    console.log("语音消息已通过WebSocket发送");
                  }
                  console.log("语音消息发送成功");
                } else {
                  tempMessage.status = "failed";
                  console.error("语音消息发送失败:", result);
                  uni.showToast({
                    title: (result === null || result === void 0 ? void 0 : result.message) || "发送失败",
                    icon: "none"
                  });
                }
                _context4.next = 22;
                break;
              case 17:
                _context4.prev = 17;
                _context4.t0 = _context4["catch"](4);
                console.error("发送语音消息失败:", _context4.t0);
                tempMessage.status = "failed";
                uni.showToast({
                  title: "发送失败",
                  icon: "none"
                });
              case 22:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[4, 17]]);
      }))();
    },
    // 上传语音文件
    uploadVoiceFile: function uploadVoiceFile(tempFilePath) {
      var _this13 = this;
      return new Promise(function (resolve, reject) {
        var token = uni.getStorageSync("token");
        var currentUserId = uni.getStorageSync("userid");
        var baseUrl = _this13.getApiBaseUrl();
        console.log("开始上传语音文件:", tempFilePath);
        uni.uploadFile({
          url: "".concat(baseUrl, "/api/upload/voice"),
          filePath: tempFilePath,
          name: "file",
          formData: {
            driver: "cos"
          },
          header: {
            bausertoken: token,
            userid: currentUserId
          },
          success: function success(uploadFileRes) {
            console.log("语音文件上传响应:", uploadFileRes);
            try {
              var result = JSON.parse(uploadFileRes.data);
              console.log("语音文件上传结果:", result);
              if (result.code === 0 && result.data) {
                resolve({
                  url: result.data.url || result.data,
                  size: result.data.size || 0
                });
              } else {
                console.error("语音文件上传失败:", result);
                reject(new Error(result.message || "上传失败"));
              }
            } catch (parseError) {
              console.error("解析上传响应失败:", parseError);
              reject(new Error("解析响应失败"));
            }
          },
          fail: function fail(error) {
            console.error("语音文件上传请求失败:", error);
            reject(new Error("上传请求失败"));
          }
        });
      });
    },
    // 播放语音消息
    playVoice: function playVoice(message) {
      var _this14 = this;
      console.log("播放语音消息:", message);

      // 停止当前播放的语音
      if (this.currentPlayingVoice && this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.currentPlayingVoice.isPlaying = false;
      }

      // 停止其他语音播放状态
      this.messageList.forEach(function (msg) {
        if (msg.type === "voice") {
          msg.isPlaying = false;
        }
      });

      // 如果点击的是正在播放的语音，则停止播放
      if (this.currentPlayingVoice === message) {
        this.currentPlayingVoice = null;
        return;
      }

      // 开始播放新的语音
      message.isPlaying = true;
      this.currentPlayingVoice = message;

      // 创建音频上下文
      this.innerAudioContext = uni.createInnerAudioContext();
      this.innerAudioContext.src = message.content; // content存储的是语音文件URL

      // 播放开始
      this.innerAudioContext.onPlay(function () {
        console.log("语音开始播放");
      });

      // 播放结束
      this.innerAudioContext.onEnded(function () {
        console.log("语音播放结束");
        message.isPlaying = false;
        _this14.currentPlayingVoice = null;
        if (_this14.innerAudioContext) {
          _this14.innerAudioContext.destroy();
          _this14.innerAudioContext = null;
        }
      });

      // 播放错误
      this.innerAudioContext.onError(function (error) {
        console.error("语音播放错误:", error);
        message.isPlaying = false;
        _this14.currentPlayingVoice = null;
        uni.showToast({
          title: "播放失败",
          icon: "none"
        });
        if (_this14.innerAudioContext) {
          _this14.innerAudioContext.destroy();
          _this14.innerAudioContext = null;
        }
      });

      // 开始播放
      this.innerAudioContext.play();
    },
    // 清理音频资源
    cleanupAudio: function cleanupAudio() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.innerAudioContext.destroy();
        this.innerAudioContext = null;
      }
      if (this.currentPlayingVoice) {
        this.currentPlayingVoice.isPlaying = false;
        this.currentPlayingVoice = null;
      }
    },
    // 获取API基础URL
    getApiBaseUrl: function getApiBaseUrl() {
      // 根据环境返回不同的API地址
      var systemInfo = uni.getSystemInfoSync();
      if (true) {
        return "http://localhost:8101";
      } else {}
    },
    toggleEmojis: function toggleEmojis() {
      this.showEmojis = !this.showEmojis;
      this.showExtensions = false;
    },
    toggleExtensions: function toggleExtensions() {
      this.showExtensions = !this.showExtensions;
      this.showEmojis = false;
    },
    insertEmoji: function insertEmoji(emoji) {
      this.inputText += emoji;
    },
    onInputFocus: function onInputFocus() {
      var _this15 = this;
      console.log("输入框获得焦点");
      this.inputFocused = true;
      this.showEmojis = false;
      this.showExtensions = false;

      // 滚动到底部，确保输入框可见
      this.$nextTick(function () {
        _this15.scrollToBottom();
      });
    },
    onInputBlur: function onInputBlur() {
      console.log("输入框失去焦点");
      this.inputFocused = false;

      // 停止输入状态提示
      this.stopTypingIndicator();
    },
    onInputChange: function onInputChange(e) {
      var value = e.detail.value;
      this.inputText = value;

      // 发送正在输入状态
      this.sendTypingIndicator();

      // 输入验证
      if (value.length > 500) {
        uni.showToast({
          title: "消息长度不能超过500字",
          icon: "none",
          duration: 1500
        });
      }
    },
    onLineChange: function onLineChange(e) {
      // 处理行数变化，可以用于调整输入框高度
      console.log("输入框行数变化:", e.detail);
    },
    onInputConfirm: function onInputConfirm() {
      // 处理键盘确认事件（如果需要支持回车发送）
      if (this.canSendMessage) {
        this.sendMessage();
      }
    },
    // 发送正在输入状态
    sendTypingIndicator: function sendTypingIndicator() {
      var _this16 = this;
      if (this.isTyping) return;
      this.isTyping = true;

      // 通过WebSocket发送正在输入状态
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "typing",
            data: {
              userId: uni.getStorageSync("userid"),
              chatId: this.chatId,
              isTyping: true
            }
          })
        });
      }

      // 3秒后自动停止输入状态
      if (this.typingTimer) {
        clearTimeout(this.typingTimer);
      }
      this.typingTimer = setTimeout(function () {
        _this16.stopTypingIndicator();
      }, 3000);
    },
    // 停止正在输入状态
    stopTypingIndicator: function stopTypingIndicator() {
      if (!this.isTyping) return;
      this.isTyping = false;
      if (this.typingTimer) {
        clearTimeout(this.typingTimer);
        this.typingTimer = null;
      }

      // 通过WebSocket发送停止输入状态
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "typing",
            data: {
              userId: uni.getStorageSync("userid"),
              chatId: this.chatId,
              isTyping: false
            }
          })
        });
      }
    },
    goBack: function goBack() {
      uni.navigateBack();
    },
    makeCall: function makeCall() {
      uni.makePhoneCall({
        phoneNumber: "10086"
      });
    },
    showMoreActions: function showMoreActions() {
      var _this17 = this;
      uni.showActionSheet({
        itemList: ["查看资料", "清空聊天记录", "举报"],
        success: function success(res) {
          switch (res.tapIndex) {
            case 0:
              _this17.viewUserProfile();
              break;
            case 1:
              _this17.clearChatHistory();
              break;
            case 2:
              _this17.reportUser();
              break;
          }
        }
      });
    },
    // 查看用户资料
    viewUserProfile: function viewUserProfile() {
      uni.navigateTo({
        url: "/pagesSub/social/user/profile?userId=".concat(this.chatId, "&name=").concat(this.chatName)
      });
    },
    // 清空聊天记录
    clearChatHistory: function clearChatHistory() {
      var _this18 = this;
      uni.showModal({
        title: "清空聊天记录",
        content: "确定要清空与该用户的所有聊天记录吗？此操作不可恢复。",
        confirmText: "清空",
        confirmColor: "#ff4757",
        success: function success(res) {
          if (res.confirm) {
            // 清空消息列表
            _this18.messageList = [];

            // 显示成功提示
            uni.showToast({
              title: "聊天记录已清空",
              icon: "success",
              duration: 2000
            });

            // 这里可以调用后端API清空服务器端的聊天记录
            _this18.clearChatHistoryFromServer();
          }
        }
      });
    },
    // 从服务器清空聊天记录
    clearChatHistoryFromServer: function clearChatHistoryFromServer() {
      var _this19 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var result;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return (0, _socialApi.clearChatHistory)(_this19.chatId);
              case 3:
                result = _context5.sent;
                if (result && result.code === 0) {
                  console.log("服务器聊天记录已清空");
                } else {
                  console.error("清空聊天记录失败:", (result === null || result === void 0 ? void 0 : result.message) || "未知错误");
                  uni.showToast({
                    title: (result === null || result === void 0 ? void 0 : result.message) || "清空失败，请重试",
                    icon: "error",
                    duration: 2000
                  });
                }
                _context5.next = 11;
                break;
              case 7:
                _context5.prev = 7;
                _context5.t0 = _context5["catch"](0);
                console.error("清空聊天记录失败:", _context5.t0);
                uni.showToast({
                  title: "清空失败，请重试",
                  icon: "error",
                  duration: 2000
                });
              case 11:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 7]]);
      }))();
    },
    // 举报用户
    reportUser: function reportUser() {
      var _this20 = this;
      var reportReasons = ["发送垃圾信息", "发送不当内容", "骚扰他人", "诈骗行为", "其他违规行为"];
      uni.showActionSheet({
        itemList: reportReasons,
        success: function success(res) {
          var reason = reportReasons[res.tapIndex];
          _this20.confirmReport(reason);
        }
      });
    },
    // 确认举报
    confirmReport: function confirmReport(reason) {
      var _this21 = this;
      uni.showModal({
        title: "举报用户",
        content: "\u786E\u5B9A\u8981\u4E3E\u62A5\u8BE5\u7528\u6237\"".concat(reason, "\"\u5417\uFF1F\u6211\u4EEC\u4F1A\u8BA4\u771F\u5904\u7406\u60A8\u7684\u4E3E\u62A5\u3002"),
        confirmText: "举报",
        confirmColor: "#ff4757",
        success: function success(res) {
          if (res.confirm) {
            // 提交举报
            _this21.submitReport(reason);
          }
        }
      });
    },
    // 提交举报到服务器
    submitReport: function submitReport(reason) {
      var _this22 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var reportData, result;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                // 显示加载提示
                uni.showLoading({
                  title: "提交中..."
                });
                _context6.prev = 1;
                reportData = {
                  reportedUserId: _this22.chatId,
                  reportedUserName: _this22.chatName,
                  reason: reason,
                  reportType: "chat",
                  description: "\u5728\u804A\u5929\u4E2D\u4E3E\u62A5\u7528\u6237\uFF1A".concat(reason),
                  timestamp: new Date().toISOString()
                };
                _context6.next = 5;
                return (0, _socialApi.reportUser)(reportData);
              case 5:
                result = _context6.sent;
                uni.hideLoading();
                if (result && result.code === 0) {
                  uni.showToast({
                    title: "举报已提交",
                    icon: "success",
                    duration: 2000
                  });

                  // 记录举报成功
                  console.log("举报提交成功:", result);
                } else {
                  console.error("举报提交失败:", (result === null || result === void 0 ? void 0 : result.message) || "未知错误");
                  uni.showToast({
                    title: (result === null || result === void 0 ? void 0 : result.message) || "提交失败，请重试",
                    icon: "error",
                    duration: 2000
                  });
                }
                _context6.next = 15;
                break;
              case 10:
                _context6.prev = 10;
                _context6.t0 = _context6["catch"](1);
                uni.hideLoading();
                console.error("举报提交失败:", _context6.t0);
                uni.showToast({
                  title: "提交失败，请重试",
                  icon: "error",
                  duration: 2000
                });
              case 15:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6, null, [[1, 10]]);
      }))();
    },
    loadMoreMessages: function loadMoreMessages() {
      // 加载更多历史消息
    },
    resendMessage: function resendMessage(message) {
      message.status = "sending";
      setTimeout(function () {
        message.status = "sent";
      }, 500);
    },
    chooseLocation: function chooseLocation() {
      this.showExtensions = false;
      this.$u.toast("位置功能开发中");
    },
    chooseFile: function chooseFile() {
      this.showExtensions = false;
      this.$u.toast("文件功能开发中");
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 772:
/*!*********************************************************************************************************************************************!*\
  !*** D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& */ 773);
/* harmony import */ var _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_developmentTool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_7a033bbb_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 773:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[766,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesSub/social/chat/detail.js.map