<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.UserStatsMapper">

    <!-- 增加关注数 -->
    <update id="incrementFollowingCount">
        INSERT INTO user_stats (user_id, following_count)
        VALUES (#{userId}, #{increment})
        ON DUPLICATE KEY UPDATE 
            following_count = GREATEST(following_count + #{increment}, 0)
    </update>

    <!-- 增加粉丝数 -->
    <update id="incrementFollowerCount">
        INSERT INTO user_stats (user_id, follower_count)
        VALUES (#{userId}, #{increment})
        ON DUPLICATE KEY UPDATE 
            follower_count = GREATEST(follower_count + #{increment}, 0)
    </update>

    <!-- 增加帖子数 -->
    <update id="incrementPostCount">
        INSERT INTO user_stats (user_id, post_count)
        VALUES (#{userId}, #{increment})
        ON DUPLICATE KEY UPDATE 
            post_count = GREATEST(post_count + #{increment}, 0)
    </update>

    <!-- 增加收到的点赞数 -->
    <update id="incrementLikeReceivedCount">
        INSERT INTO user_stats (user_id, like_received_count)
        VALUES (#{userId}, #{increment})
        ON DUPLICATE KEY UPDATE 
            like_received_count = GREATEST(like_received_count + #{increment}, 0)
    </update>

    <!-- 初始化用户统计数据 -->
    <insert id="initUserStats">
        INSERT IGNORE INTO user_stats (user_id, following_count, follower_count, post_count, like_received_count)
        VALUES (#{userId}, 0, 0, 0, 0)
    </insert>

</mapper>
