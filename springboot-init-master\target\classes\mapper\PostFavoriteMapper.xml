<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostFavoriteMapper">

    <resultMap id="PostVOResultMap" type="com.yupi.springbootinit.model.vo.PostVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="images" column="images" jdbcType="VARCHAR" typeHandler="com.yupi.springbootinit.config.ListJsonTypeHandler"/>
        <result property="locationName" column="location_name" jdbcType="VARCHAR"/>
        <result property="locationLatitude" column="location_latitude" jdbcType="DECIMAL"/>
        <result property="locationLongitude" column="location_longitude" jdbcType="DECIMAL"/>
        <result property="locationAddress" column="location_address" jdbcType="VARCHAR"/>
        <result property="likeCount" column="like_count" jdbcType="INTEGER"/>
        <result property="commentCount" column="comment_count" jdbcType="INTEGER"/>
        <result property="shareCount" column="share_count" jdbcType="INTEGER"/>
        <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isLiked" column="is_liked" jdbcType="BOOLEAN"/>
        <result property="isFavorited" column="is_favorited" jdbcType="BOOLEAN"/>
        <result property="isOwner" column="is_owner" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 查询用户是否已收藏帖子 -->
    <select id="selectByPostIdAndUserId" resultType="com.yupi.springbootinit.model.entity.PostFavorite">
        SELECT * FROM post_favorites 
        WHERE post_id = #{postId} AND user_id = #{userId}
        LIMIT 1
    </select>

    <!-- 查询用户收藏的帖子列表 -->
    <select id="selectFavoritePostsByUserId" resultMap="PostVOResultMap">
        SELECT
            p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude,
            p.location_longitude, p.location_address, p.like_count, p.comment_count,
            p.share_count, p.view_count, p.create_time,
            u.nickname, u.avatar, u.level,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            1 as is_favorited,
            CASE WHEN p.user_id = #{userId} THEN 1 ELSE 0 END as is_owner
        FROM post_favorites pf
        INNER JOIN posts p ON pf.post_id = p.id AND p.is_delete = 0 AND p.status = 1
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{userId} AND pl.is_delete = 0
        WHERE pf.user_id = #{userId} AND pf.is_delete = 0
        ORDER BY pf.create_time DESC
    </select>

    <!-- 统计帖子收藏数 -->
    <select id="countByPostId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_favorites
        WHERE post_id = #{postId}
    </select>

    <!-- 统计用户收藏数 -->
    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_favorites
        WHERE user_id = #{userId}
    </select>

    <!-- 批量查询用户对帖子的收藏状态 -->
    <select id="selectBatchByPostIds" resultType="com.yupi.springbootinit.model.entity.PostFavorite">
        SELECT * FROM post_favorites
        WHERE user_id = #{userId}
        AND post_id IN
        <foreach collection="postIds" item="postId" open="(" separator="," close=")">
            #{postId}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户收藏的帖子列表（新方法名） -->
    <select id="selectUserFavoritePosts" resultMap="PostVOResultMap">
        SELECT
            p.id, p.user_id, p.content, p.images, p.location_name, p.location_latitude,
            p.location_longitude, p.location_address, p.like_count, p.comment_count,
            p.share_count, p.view_count, p.create_time,
            u.nickname, u.avatar, u.level,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            1 as is_favorited,
            CASE WHEN p.user_id = #{userId} THEN 1 ELSE 0 END as is_owner
        FROM post_favorites pf
        INNER JOIN posts p ON pf.post_id = p.id AND p.is_delete = 0 AND p.status = 1
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{userId} AND pl.is_delete = 0
        WHERE pf.user_id = #{userId} AND pf.is_delete = 0
        ORDER BY pf.create_time DESC
    </select>

</mapper>
