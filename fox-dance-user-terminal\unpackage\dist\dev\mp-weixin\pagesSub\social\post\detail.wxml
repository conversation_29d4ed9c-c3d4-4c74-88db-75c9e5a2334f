<view class="detail-container data-v-0722cd1a"><block wx:if="{{loading.page}}"><view class="page-loading data-v-0722cd1a"><view class="loading-container data-v-0722cd1a"><view class="loading-spinner data-v-0722cd1a"><u-loading vue-id="dd35d41a-1" mode="circle" size="40" color="#ff6b87" class="data-v-0722cd1a" bind:__l="__l"></u-loading></view><text class="loading-text data-v-0722cd1a">加载中...</text></view></view></block><block wx:else><scroll-view class="content data-v-0722cd1a" scroll-y="{{true}}"><view class="post-detail data-v-0722cd1a"><block wx:if="{{loading.post}}"><view class="post-loading data-v-0722cd1a"><view class="skeleton-user data-v-0722cd1a"><view class="skeleton-avatar data-v-0722cd1a"></view><view class="skeleton-user-info data-v-0722cd1a"><view class="skeleton-line skeleton-username data-v-0722cd1a"></view><view class="skeleton-line skeleton-time data-v-0722cd1a"></view></view></view><view class="skeleton-content data-v-0722cd1a"><view class="skeleton-line skeleton-title data-v-0722cd1a"></view><view class="skeleton-line skeleton-text data-v-0722cd1a"></view><view class="skeleton-line skeleton-text short data-v-0722cd1a"></view></view></view></block><block wx:else><view class="user-info data-v-0722cd1a"><u-avatar vue-id="dd35d41a-2" src="{{postData.userAvatar}}" size="50" data-event-opts="{{[['^click',[['goUserProfile']]]]}}" bind:click="__e" class="data-v-0722cd1a" bind:__l="__l"></u-avatar><view class="user-details data-v-0722cd1a"><text data-event-opts="{{[['tap',[['goUserProfile',['$event']]]]]}}" class="username data-v-0722cd1a" bindtap="__e">{{postData.username}}</text><text class="time data-v-0722cd1a">{{$root.m0}}</text></view><block wx:if="{{isOwnPost}}"><view class="post-actions data-v-0722cd1a"><view data-event-opts="{{[['tap',[['showPostActions',['$event']]]]]}}" class="action-btn data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-3" name="more-dot-fill" color="#666" size="20" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view></view></block><block wx:else><follow-button vue-id="dd35d41a-4" user="{{$root.a0}}" followed="{{postData.isFollowed}}" size="mini" data-event-opts="{{[['^follow',[['onUserFollow']]],['^change',[['onFollowChange']]]]}}" bind:follow="__e" bind:change="__e" class="data-v-0722cd1a" bind:__l="__l"></follow-button></block></view><block wx:if="{{postData.title}}"><view class="post-title data-v-0722cd1a"><text class="title-text data-v-0722cd1a">{{postData.title}}</text></view></block><view class="post-content data-v-0722cd1a"><text class="content-text data-v-0722cd1a">{{postData.content}}</text></view></block><block wx:if="{{$root.g0}}"><view class="topic-tags data-v-0722cd1a"><block wx:for="{{postData.topics}}" wx:for-item="topic" wx:for-index="__i0__" wx:key="*this"><text data-event-opts="{{[['tap',[['goTopic',['$0'],[[['postData.topics','',__i0__]]]]]]]}}" class="topic-tag data-v-0722cd1a" bindtap="__e">{{"#"+topic}}</text></block></view></block><block wx:if="{{$root.g1}}"><view class="post-images data-v-0722cd1a"><swiper class="image-swiper data-v-0722cd1a" indicator-dots="{{$root.g2>1}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#fff"><block wx:for="{{$root.l0}}" wx:for-item="img" wx:for-index="index" wx:key="index"><swiper-item class="data-v-0722cd1a"><image class="swiper-image data-v-0722cd1a" src="{{img.m1}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper></view></block><block wx:if="{{postData.location}}"><view data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" class="location-info data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-5" name="map" color="#999" size="16" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="location-text data-v-0722cd1a">{{postData.location}}</text></view></block><view class="post-stats data-v-0722cd1a"><text class="stat-item data-v-0722cd1a">{{postData.likeCount+"人点赞"}}</text><text class="stat-item data-v-0722cd1a">{{postData.commentCount+"条评论"}}</text><text class="stat-item data-v-0722cd1a">{{postData.shareCount+"次分享"}}</text></view><view class="action-bar data-v-0722cd1a"><view data-event-opts="{{[['tap',[['toggleLike',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-6" name="{{postData.isLiked?'heart-fill':'heart'}}" color="{{postData.isLiked?'#ff4757':'#666'}}" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">{{"点赞 ("+(postData.likeCount||0)+")"}}</text></view><view data-event-opts="{{[['tap',[['focusComment',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-7" name="chat" color="#666" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">评论</text></view><view data-event-opts="{{[['tap',[['sharePost',['$event']]]]]}}" class="action-item data-v-0722cd1a" bindtap="__e"><u-icon vue-id="dd35d41a-8" name="share" color="#666" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="action-text data-v-0722cd1a">分享</text></view></view></view><view class="comments-section data-v-0722cd1a"><view class="comments-header data-v-0722cd1a"><text class="comments-title data-v-0722cd1a">{{"评论 "+(loading.comments?'...':$root.g3)}}</text><u-tabs vue-id="dd35d41a-9" list="{{tabList}}" current="{{currentTab}}" scrollable="{{false}}" activeColor="#2979ff" inactiveColor="#999" fontSize="28" lineColor="#2979ff" lineWidth="20" lineHeight="3" height="40" data-event-opts="{{[['^change',[['changeSortType']]]]}}" bind:change="__e" class="data-v-0722cd1a" bind:__l="__l"></u-tabs></view><block wx:if="{{loading.comments}}"><view class="comments-loading data-v-0722cd1a"><block wx:for="{{3}}" wx:for-item="n" wx:for-index="__i1__" wx:key="*this"><view class="skeleton-comment data-v-0722cd1a"><view class="skeleton-avatar data-v-0722cd1a"></view><view class="skeleton-comment-content data-v-0722cd1a"><view class="skeleton-line skeleton-comment-user data-v-0722cd1a"></view><view class="skeleton-line skeleton-comment-text data-v-0722cd1a"></view><view class="skeleton-line skeleton-comment-text short data-v-0722cd1a"></view></view></view></block></view></block><block wx:else><view class="comment-list data-v-0722cd1a"><block wx:if="{{$root.g4===0}}"><view class="comment-empty data-v-0722cd1a"><view class="empty-icon data-v-0722cd1a"><u-icon vue-id="dd35d41a-10" name="chat" size="60" color="#d0d0d0" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><view class="empty-text data-v-0722cd1a">暂无评论</view><view class="empty-desc data-v-0722cd1a">快来发表第一条评论吧~</view></view></block><block wx:else><block wx:for="{{$root.l2}}" wx:for-item="comment" wx:for-index="__i2__" wx:key="id"><view class="comment-item data-v-0722cd1a"><view data-event-opts="{{[['tap',[['goCommentUserProfile',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="user-avatar data-v-0722cd1a" catchtap="__e"><u-avatar vue-id="{{'dd35d41a-11-'+__i2__}}" src="{{comment.$orig.userAvatar}}" size="40" class="data-v-0722cd1a" bind:__l="__l"></u-avatar></view><view class="comment-content data-v-0722cd1a"><view class="user-info-row data-v-0722cd1a"><view class="user-info data-v-0722cd1a"><view data-event-opts="{{[['tap',[['goCommentUserProfile',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="user-name data-v-0722cd1a" catchtap="__e">{{''+comment.$orig.username+''}}<block wx:if="{{comment.$orig.level>=0}}"><view class="user-level data-v-0722cd1a" style="{{'background-color:'+(comment.m2)+';'}}">{{"Lv."+comment.$orig.level}}</view></block></view><view class="time data-v-0722cd1a">{{comment.m3}}</view></view><view data-event-opts="{{[['tap',[['toggleCommentLike',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="{{['like-btn','data-v-0722cd1a',(comment.$orig.isLiked)?'liked':'']}}" catchtap="__e"><u-icon vue-id="{{'dd35d41a-12-'+__i2__}}" name="{{comment.$orig.isLiked?'heart-fill':'heart'}}" color="{{comment.$orig.isLiked?'#ff4757':'#666'}}" size="20" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="data-v-0722cd1a">{{comment.$orig.likeCount||0}}</text></view></view><view class="text data-v-0722cd1a"><text class="data-v-0722cd1a">{{comment.$orig.showFullContent?comment.$orig.content:comment.g5>100?comment.g6+'...':comment.$orig.content}}</text><block wx:if="{{comment.g7>100}}"><view data-event-opts="{{[['tap',[['toggleContent',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="expand-btn data-v-0722cd1a" catchtap="__e">{{comment.$orig.showFullContent?'收起':'展开'}}</view></block></view><view class="actions data-v-0722cd1a"><view data-event-opts="{{[['tap',[['replyComment',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="reply-btn data-v-0722cd1a" catchtap="__e"><u-icon vue-id="{{'dd35d41a-13-'+__i2__}}" name="chat" color="#666" size="18" class="data-v-0722cd1a" bind:__l="__l"></u-icon><text class="data-v-0722cd1a">回复</text></view><view data-event-opts="{{[['tap',[['showMoreOptions',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="more-btn data-v-0722cd1a" catchtap="__e"><u-icon vue-id="{{'dd35d41a-14-'+__i2__}}" name="more-dot-fill" color="#999" size="18" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view></view><block wx:if="{{comment.g8}}"><view class="reply-preview data-v-0722cd1a"><block wx:for="{{comment.l1}}" wx:for-item="reply" wx:for-index="rIndex" wx:key="rIndex"><view class="reply-item data-v-0722cd1a"><text data-event-opts="{{[['tap',[['goReplyUserProfile',['$0'],[[['commentList','id',comment.$orig.id],['replies.slice(0,2)','',rIndex]]]]]]]}}" class="reply-nickname data-v-0722cd1a" catchtap="__e">{{reply.$orig.username}}</text><block wx:if="{{reply.$orig.replyTo}}"><text data-event-opts="{{[['tap',[['goReplyToUserProfile',['$0'],[[['commentList','id',comment.$orig.id],['replies.slice(0,2)','',rIndex,'replyTo']]]]]]]}}" class="reply-to data-v-0722cd1a" catchtap="__e">{{"@"+reply.$orig.replyTo.username}}</text></block><text class="reply-content data-v-0722cd1a">{{": "+(reply.g9>50?reply.g10+'...':reply.$orig.content)}}</text></view></block><block wx:if="{{comment.$orig.replyCount>2}}"><view data-event-opts="{{[['tap',[['viewAllReplies',['$0'],[[['commentList','id',comment.$orig.id]]]]]]]}}" class="view-more data-v-0722cd1a" catchtap="__e">{{"查看全部"+comment.$orig.replyCount+"条回复 >"}}</view></block></view></block></view></view></block></block></view></block></view></scroll-view></block><view class="comment-input-bar data-v-0722cd1a"><block wx:if="{{isReplyMode}}"><view class="reply-indicator data-v-0722cd1a"><view class="reply-info data-v-0722cd1a"><text class="reply-text data-v-0722cd1a">{{"回复 @"+(currentReply&&currentReply.username?currentReply.username:'用户')}}</text><view data-event-opts="{{[['tap',[['cancelReplyMode',['$event']]]]]}}" class="cancel-reply-btn data-v-0722cd1a" bindtap="__e"><text class="data-v-0722cd1a">✕</text></view></view></view></block><view class="input-container data-v-0722cd1a"><u-avatar vue-id="dd35d41a-15" src="{{currentUser.avatar}}" size="32" class="data-v-0722cd1a" bind:__l="__l"></u-avatar><input class="comment-input data-v-0722cd1a vue-ref" placeholder="{{inputPlaceholder}}" data-ref="commentInput" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['input',[['__set_model',['','commentText','$event',[]]]]]]}}" value="{{commentText}}" bindfocus="__e" bindblur="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['sendComment',['$event']]]]]}}" class="{{['send-btn','data-v-0722cd1a',($root.g11)?'active':'']}}" bindtap="__e">发送</text></view></view><u-popup bind:input="__e" vue-id="dd35d41a-16" mode="bottom" border-radius="30" value="{{showMorePopup}}" data-event-opts="{{[['^input',[['__set_model',['','showMorePopup','$event',[]]]]]]}}" class="data-v-0722cd1a" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-popup data-v-0722cd1a"><view data-event-opts="{{[['tap',[['replyFromMore',['$event']]]]]}}" class="action-item reply data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-17')+','+('dd35d41a-16')}}" name="chat" color="#ff6b87" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">回复</text></view><view data-event-opts="{{[['tap',[['copyComment',['$event']]]]]}}" class="action-item copy data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-18')+','+('dd35d41a-16')}}" name="file-text" color="#666" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">复制</text></view><block wx:if="{{$root.m4}}"><view data-event-opts="{{[['tap',[['deleteComment',['$event']]]]]}}" class="action-item delete data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-19')+','+('dd35d41a-16')}}" name="trash" color="#f56c6c" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">删除</text></view></block><view data-event-opts="{{[['tap',[['reportComment',['$event']]]]]}}" class="action-item report data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-20')+','+('dd35d41a-16')}}" name="info-circle" color="#999" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">举报</text></view></view></u-popup><u-popup bind:input="__e" vue-id="dd35d41a-21" mode="bottom" border-radius="30" value="{{showPostActionsPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPostActionsPopup','$event',[]]]]]]}}" class="data-v-0722cd1a" bind:__l="__l" vue-slots="{{['default']}}"><view class="action-popup data-v-0722cd1a"><view class="popup-header data-v-0722cd1a"><text class="popup-title data-v-0722cd1a">帖子操作</text></view><view data-event-opts="{{[['tap',[['editPost',['$event']]]]]}}" class="action-item edit data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-22')+','+('dd35d41a-21')}}" name="edit-pen" color="#2979ff" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">编辑帖子</text></view><view data-event-opts="{{[['tap',[['setPostPermission',['$event']]]]]}}" class="action-item permission data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-23')+','+('dd35d41a-21')}}" name="lock" color="#67C23A" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">权限设置</text></view><view data-event-opts="{{[['tap',[['deletePost',['$event']]]]]}}" class="action-item delete data-v-0722cd1a" bindtap="__e"><view class="action-icon data-v-0722cd1a"><u-icon vue-id="{{('dd35d41a-24')+','+('dd35d41a-21')}}" name="trash" color="#f56c6c" size="24" class="data-v-0722cd1a" bind:__l="__l"></u-icon></view><text class="data-v-0722cd1a">删除帖子</text></view></view></u-popup></view>