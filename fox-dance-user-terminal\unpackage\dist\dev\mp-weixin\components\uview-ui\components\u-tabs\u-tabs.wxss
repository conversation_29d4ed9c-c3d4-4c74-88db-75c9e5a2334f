@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
view.data-v-d9072e9a,
scroll-view.data-v-d9072e9a {
  box-sizing: border-box;
}
.data-v-d9072e9a::-webkit-scrollbar,.data-v-d9072e9a::-webkit-scrollbar,.data-v-d9072e9a::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
.u-scroll-box.data-v-d9072e9a {
  position: relative;
}
.u-scroll-view.data-v-d9072e9a {
  width: 100%;
  white-space: nowrap;
  position: relative;
}
.u-tab-item.data-v-d9072e9a {
  position: relative;
  display: inline-block;
  text-align: center;
  transition-property: background-color, color;
}
.u-tab-bar.data-v-d9072e9a {
  position: absolute;
  bottom: 0;
}
.u-tabs-scroll-flex.data-v-d9072e9a {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

