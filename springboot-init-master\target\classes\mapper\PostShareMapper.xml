<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostShareMapper">

    <!-- 查询帖子分享记录 -->
    <select id="selectByPostIdAndUserId" resultType="com.yupi.springbootinit.model.entity.PostShare">
        SELECT * FROM post_shares
        WHERE post_id = #{postId} AND user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 统计帖子分享数 -->
    <select id="countByPostId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_shares
        WHERE post_id = #{postId}
    </select>

    <!-- 统计用户分享数 -->
    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_shares
        WHERE user_id = #{userId}
    </select>

    <!-- 按分享类型统计 -->
    <select id="countByShareType" resultType="java.util.HashMap">
        SELECT share_type as `key`, COUNT(*) as `value`
        FROM post_shares
        WHERE post_id = #{postId}
        GROUP BY share_type
    </select>

</mapper>
