<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostStatsMapper">

    <!-- 增加点赞数 -->
    <update id="incrementLikeCount">
        INSERT INTO post_stats (post_id, like_count, last_activity_time)
        VALUES (#{postId}, #{increment}, NOW())
        ON DUPLICATE KEY UPDATE 
            like_count = GREATEST(like_count + #{increment}, 0),
            last_activity_time = NOW()
    </update>

    <!-- 增加评论数 -->
    <update id="incrementCommentCount">
        INSERT INTO post_stats (post_id, comment_count, last_activity_time)
        VALUES (#{postId}, #{increment}, NOW())
        ON DUPLICATE KEY UPDATE 
            comment_count = GREATEST(comment_count + #{increment}, 0),
            last_activity_time = NOW()
    </update>

    <!-- 增加分享数 -->
    <update id="incrementShareCount">
        INSERT INTO post_stats (post_id, share_count, last_activity_time)
        VALUES (#{postId}, #{increment}, NOW())
        ON DUPLICATE KEY UPDATE
            share_count = GREATEST(share_count + #{increment}, 0),
            last_activity_time = NOW()
    </update>

    <!-- 增加收藏数 -->
    <update id="incrementFavoriteCount">
        INSERT INTO post_stats (post_id, favorite_count, last_activity_time)
        VALUES (#{postId}, #{increment}, NOW())
        ON DUPLICATE KEY UPDATE
            favorite_count = GREATEST(favorite_count + #{increment}, 0),
            last_activity_time = NOW()
    </update>

    <!-- 增加浏览数 -->
    <update id="incrementViewCount">
        INSERT INTO post_stats (post_id, view_count, last_activity_time)
        VALUES (#{postId}, #{increment}, NOW())
        ON DUPLICATE KEY UPDATE
            view_count = view_count + #{increment},
            last_activity_time = NOW()
    </update>

    <!-- 更新最后活跃时间 -->
    <update id="updateLastActivityTime">
        UPDATE post_stats
        SET last_activity_time = NOW()
        WHERE post_id = #{postId}
    </update>

    <!-- 初始化帖子统计数据 -->
    <insert id="initPostStats">
        INSERT IGNORE INTO post_stats (post_id, like_count, comment_count, share_count, view_count, last_activity_time)
        VALUES (#{postId}, 0, 0, 0, 0, NOW())
    </insert>

</mapper>
