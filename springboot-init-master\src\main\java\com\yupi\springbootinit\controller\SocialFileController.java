package com.yupi.springbootinit.controller;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.yupi.springbootinit.common.BaseResponse;
import com.yupi.springbootinit.common.ErrorCode;
import com.yupi.springbootinit.common.ResultUtils;
import com.yupi.springbootinit.config.CosClientConfig;
import com.yupi.springbootinit.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 社交模块文件上传接口
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@RestController
@RequestMapping("/upload")
@Slf4j
@Api(tags = "社交模块文件上传接口")
public class SocialFileController {

    @Resource
    private COSClient cosClient;

    @Resource
    private CosClientConfig cosClientConfig;

    /**
     * 上传用户头像
     */
    @PostMapping("/avatar")
    @ApiOperation(value = "上传用户头像")
    public BaseResponse<String> uploadAvatar(@RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        log.info("接收到头像上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 5 * 1024 * 1024); // 头像限制5MB

        try {
            String fileName = generateFileName("avatar", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);

            log.info("头像上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);

        } catch (Exception e) {
            log.error("头像上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传帖子图片（自动转换为webp格式）
     *
     * 功能特点：
     * 1. 自动使用腾讯云数据万象将图片转换为webp格式
     * 2. webp格式相比原格式可减少30-50%的文件大小
     * 3. 大幅减少带宽消耗，提升用户加载速度
     * 4. 如果转换失败会自动回退到原格式上传
     * 5. 设置长期缓存策略，优化CDN性能
     */
    @PostMapping("/post-image")
    @ApiOperation(value = "上传帖子图片")
    public BaseResponse<Map<String, Object>> uploadPostImage(@RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        log.info("接收到帖子图片上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 10 * 1024 * 1024); // 帖子图片限制10MB

        try {
            // 生成webp格式的文件名
            String fileName = generateFileName("posts", "webp");

            // 使用数据万象转换为webp格式并上传
            String fileUrl = uploadToCoSWithWebpConversion(file, fileName);

            // 生成缩略图URL作为封面
            String thumbnailUrl = generateThumbnailUrl(fileUrl);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("imageUrl", fileUrl); // 原图URL
            result.put("thumbnailUrl", thumbnailUrl); // 缩略图URL（封面）

            log.info("帖子图片上传成功，原图: {}, 缩略图: {}", fileUrl, thumbnailUrl);
            return ResultUtils.success(result);

        } catch (Exception e) {
            log.error("帖子图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "帖子图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传帖子图片
     */
    @PostMapping("/post-images")
    @ApiOperation(value = "批量上传帖子图片")
    public BaseResponse<Map<String, Object>> uploadPostImages(@RequestParam("files") MultipartFile[] files,
            HttpServletRequest request) {
        log.info("接收到批量帖子图片上传请求，文件数量: {}", files.length);

        if (files.length > 9) {
            return ResultUtils.error(ErrorCode.PARAMS_ERROR, "最多只能上传9张图片");
        }

        List<String> fileUrls = new ArrayList<>();
        String coverImageUrl = null;

        try {
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];

                // 验证每个文件
                validateImageFile(file, 10 * 1024 * 1024);

                // 生成webp格式的文件名
                String fileName = generateFileName("posts", "webp");
                // 使用数据万象转换为webp格式并上传
                String fileUrl = uploadToCoSWithWebpConversion(file, fileName);
                fileUrls.add(fileUrl);

                // 第一张图片作为封面，生成缩略图
                if (i == 0) {
                    coverImageUrl = generateThumbnailUrl(fileUrl);
                    log.info("生成封面缩略图: {}", coverImageUrl);
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("images", fileUrls);
            result.put("coverImage", coverImageUrl);

            log.info("批量帖子图片上传成功，共{}张图片，封面: {}", fileUrls.size(), coverImageUrl);
            return ResultUtils.success(result);

        } catch (Exception e) {
            log.error("批量帖子图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "批量图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传聊天图片
     */
    @PostMapping("/chat-image")
    @ApiOperation(value = "上传聊天图片")
    public BaseResponse<String> uploadChatImage(@RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        log.info("接收到聊天图片上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateImageFile(file, 5 * 1024 * 1024); // 聊天图片限制5MB

        try {
            String fileName = generateFileName("chat", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);

            log.info("聊天图片上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);

        } catch (Exception e) {
            log.error("聊天图片上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "聊天图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传语音文件
     */
    @PostMapping("/voice")
    @ApiOperation(value = "上传语音文件")
    public BaseResponse<String> uploadVoiceFile(@RequestParam("file") MultipartFile file,
            HttpServletRequest request) {
        log.info("接收到语音文件上传请求，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateVoiceFile(file, 10 * 1024 * 1024); // 语音文件限制10MB

        try {
            String fileName = generateFileName("voice", getFileExtension(file.getOriginalFilename()));
            String fileUrl = uploadToCos(file, fileName);

            log.info("语音文件上传成功，URL: {}", fileUrl);
            return ResultUtils.success(fileUrl);

        } catch (Exception e) {
            log.error("语音文件上传失败", e);
            return ResultUtils.error(ErrorCode.OPERATION_ERROR, "语音文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file, long maxSize) {
        if (file.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件不能为空");
        }

        if (file.getSize() > maxSize) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR,
                    "文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFilename);
        if (!isValidImageExtension(fileExtension)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "只支持jpg、jpeg、png、gif、webp格式的图片");
        }
    }

    /**
     * 验证语音文件
     */
    private void validateVoiceFile(MultipartFile file, long maxSize) {
        if (file.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件不能为空");
        }

        if (file.getSize() > maxSize) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR,
                    "文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFilename);
        if (!isValidVoiceExtension(fileExtension)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "只支持mp3、wav、m4a、aac格式的语音文件");
        }
    }

    /**
     * 上传文件到腾讯云COS
     */
    private String uploadToCos(MultipartFile file, String fileName) throws IOException {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    cosClientConfig.getBucket(),
                    fileName,
                    file.getInputStream(),
                    metadata);

            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.debug("COS上传结果: {}", putObjectResult.getETag());

            return cosClientConfig.getBaseUrl() + "/" + fileName;

        } catch (Exception e) {
            log.error("COS上传失败", e);
            throw new RuntimeException("COS上传失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String category, String fileExtension) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String datePath = sdf.format(new Date());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return "social/" + category + "/" + datePath + "/" + uuid + "." + fileExtension;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 验证是否为有效的图片扩展名
     */
    private boolean isValidImageExtension(String extension) {
        String[] validExtensions = { "jpg", "jpeg", "png", "gif", "webp" };
        return Arrays.asList(validExtensions).contains(extension);
    }

    /**
     * 验证是否为有效的语音文件扩展名
     */
    private boolean isValidVoiceExtension(String extension) {
        String[] validExtensions = { "mp3", "wav", "m4a", "aac" };
        return Arrays.asList(validExtensions).contains(extension);
    }

    /**
     * 使用腾讯云数据万象转换为webp格式并上传到COS
     *
     * @param file     原始图片文件
     * @param fileName 目标文件名（应该以.webp结尾）
     * @return 上传后的文件URL
     * @throws IOException IO异常
     */
    private String uploadToCoSWithWebpConversion(MultipartFile file, String fileName) throws IOException {
        try {
            log.info("开始上传图片，原文件: {}, 目标文件: {}",
                    file.getOriginalFilename(), fileName);

            // 先上传原图
            String originalExtension = getFileExtension(file.getOriginalFilename());
            String originalFileName = fileName.replace(".webp", "." + originalExtension);

            // 上传原图
            String originalUrl = uploadToCos(file, originalFileName);
            log.info("原图上传成功: {}", originalUrl);

            // 使用数据万象的下载时处理方式生成webp URL
            // 根据官方文档，这种方式不需要特殊的签名配置
            String webpUrl = originalUrl + "?imageMogr2/format/webp/quality/85";

            log.info("生成webp处理URL: {}", webpUrl);
            log.info("图片将在访问时自动转换为webp格式，预计压缩30-50%");

            // 返回webp处理URL，数据库中存储的是这个URL
            return webpUrl;

        } catch (Exception e) {
            log.error("图片上传失败，原因: {}", e.getMessage(), e);

            // 如果上传失败，回退到普通上传
            log.warn("回退到普通上传（保持原格式）");
            String originalExtension = getFileExtension(file.getOriginalFilename());
            String fallbackFileName = fileName.replace(".webp", "." + originalExtension);

            return uploadToCos(file, fallbackFileName);
        }
    }

    /**
     * 为图片生成缩略图URL
     *
     * @param originalUrl 原图URL
     * @return 缩略图URL
     */
    private String generateThumbnailUrl(String originalUrl) {
        // 使用数据万象生成缩略图：300x300，保持宽高比，webp格式
        return originalUrl + "?imageMogr2/thumbnail/300x300>/format/webp/quality/85";
    }
}
