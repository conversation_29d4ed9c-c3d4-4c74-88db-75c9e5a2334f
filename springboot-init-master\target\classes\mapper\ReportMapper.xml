<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.ReportMapper">

    <resultMap id="ReportVOResultMap" type="com.yupi.springbootinit.model.vo.ReportVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reporterId" column="reporter_id" jdbcType="BIGINT"/>
        <result property="reporterNickname" column="reporter_nickname" jdbcType="VARCHAR"/>
        <result property="reportedUserId" column="reported_user_id" jdbcType="BIGINT"/>
        <result property="reportedUserNickname" column="reported_user_nickname" jdbcType="VARCHAR"/>
        <result property="targetType" column="target_type" jdbcType="VARCHAR"/>
        <result property="targetId" column="target_id" jdbcType="BIGINT"/>
        <result property="reason" column="reason" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="handlerId" column="handler_id" jdbcType="BIGINT"/>
        <result property="handlerNickname" column="handler_nickname" jdbcType="VARCHAR"/>
        <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
        <result property="handleResult" column="handle_result" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询举报列表 -->
    <select id="selectReportPage" resultMap="ReportVOResultMap">
        SELECT 
            r.id, r.reporter_id, r.reported_user_id, r.target_type, r.target_id,
            r.reason, r.description, r.status, r.handler_id, r.handle_time,
            r.handle_result, r.create_time,
            u1.nickname as reporter_nickname,
            u2.nickname as reported_user_nickname,
            u3.nickname as handler_nickname
        FROM reports r
        LEFT JOIN ba_user u1 ON r.reporter_id = u1.id
        LEFT JOIN ba_user u2 ON r.reported_user_id = u2.id
        LEFT JOIN ba_user u3 ON r.handler_id = u3.id
        WHERE 1=1
        <if test="targetType != null and targetType != ''">
            AND r.target_type = #{targetType}
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        <if test="reason != null">
            AND r.reason = #{reason}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 查询用户的举报记录 -->
    <select id="selectUserReports" resultMap="ReportVOResultMap">
        SELECT 
            r.id, r.reporter_id, r.reported_user_id, r.target_type, r.target_id,
            r.reason, r.description, r.status, r.handler_id, r.handle_time,
            r.handle_result, r.create_time,
            u1.nickname as reporter_nickname,
            u2.nickname as reported_user_nickname,
            u3.nickname as handler_nickname
        FROM reports r
        LEFT JOIN ba_user u1 ON r.reporter_id = u1.id
        LEFT JOIN ba_user u2 ON r.reported_user_id = u2.id
        LEFT JOIN ba_user u3 ON r.handler_id = u3.id
        WHERE r.reporter_id = #{reporterId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 查询针对特定目标的举报 -->
    <select id="selectByTarget" resultType="com.yupi.springbootinit.model.entity.Report">
        SELECT * FROM reports 
        WHERE target_type = #{targetType} AND target_id = #{targetId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计待处理举报数 -->
    <select id="countPendingReports" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM reports WHERE status = 0
    </select>

    <!-- 统计各类型举报数 -->
    <select id="countReportsByType" resultType="java.util.Map">
        SELECT target_type, COUNT(*) as count
        FROM reports 
        GROUP BY target_type
        ORDER BY count DESC
    </select>

    <!-- 统计各原因举报数 -->
    <select id="countReportsByReason" resultType="java.util.Map">
        SELECT reason, COUNT(*) as count
        FROM reports 
        GROUP BY reason
        ORDER BY count DESC
    </select>

    <!-- 批量更新举报状态 -->
    <update id="batchUpdateStatus">
        UPDATE reports 
        SET status = #{status}, 
            handler_id = #{handlerId}, 
            handle_time = NOW(),
            handle_result = #{handleResult}
        WHERE id IN
        <foreach collection="reportIds" item="reportId" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </update>

    <!-- 查询用户被举报的次数 -->
    <select id="countUserReported" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM reports 
        WHERE reported_user_id = #{userId}
    </select>

    <!-- 查询内容被举报的次数 -->
    <select id="countTargetReported" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM reports 
        WHERE target_type = #{targetType} AND target_id = #{targetId}
    </select>

</mapper>
