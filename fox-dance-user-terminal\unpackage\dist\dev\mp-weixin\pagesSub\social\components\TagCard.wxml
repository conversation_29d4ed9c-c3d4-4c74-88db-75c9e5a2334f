<view data-event-opts="{{[['tap',[['goTagDetail',['$event']]]]]}}" class="tag-card data-v-093619a4" bindtap="__e"><view class="tag-icon data-v-093619a4"><block wx:if="{{tag.coverImage}}"><view class="tag-cover data-v-093619a4"><image class="cover-image data-v-093619a4" src="{{tag.coverImage}}" mode="aspectFill" data-event-opts="{{[['error',[['onImageError',['$event']]]]]}}" binderror="__e"></image></view></block><block wx:else><view class="default-icon data-v-093619a4"><u-icon vue-id="7139f398-1" name="hash" color="#ff6b87" size="32" class="data-v-093619a4" bind:__l="__l"></u-icon></view></block></view><view class="tag-info data-v-093619a4"><view class="tag-name data-v-093619a4"><text class="name-text data-v-093619a4">{{"#"+(tag.name||tag.tagName)}}</text><block wx:if="{{tag.isHot}}"><view class="hot-badge data-v-093619a4"><u-icon vue-id="7139f398-2" name="fire" color="#ff6b87" size="14" class="data-v-093619a4" bind:__l="__l"></u-icon><text class="hot-text data-v-093619a4">热门</text></view></block></view><block wx:if="{{tag.description}}"><view class="tag-description data-v-093619a4"><text class="desc-text data-v-093619a4">{{tag.description}}</text></view></block><view class="tag-stats data-v-093619a4"><view class="stat-item data-v-093619a4"><u-icon vue-id="7139f398-3" name="edit-pen" color="#909399" size="14" class="data-v-093619a4" bind:__l="__l"></u-icon><text class="stat-text data-v-093619a4">{{$root.m0+"篇帖子"}}</text></view><view class="stat-item data-v-093619a4"><u-icon vue-id="7139f398-4" name="eye" color="#909399" size="14" class="data-v-093619a4" bind:__l="__l"></u-icon><text class="stat-text data-v-093619a4">{{$root.m1+"次浏览"}}</text></view></view></view><view class="action-container data-v-093619a4"><tag-follow-button vue-id="7139f398-5" tag="{{tag}}" followed="{{tag.isFollowed}}" size="mini" data-event-opts="{{[['^click',[['',['$event']]]],['^followChange',[['onFollowChange']]]]}}" catch:click="__e" bind:followChange="__e" class="data-v-093619a4" bind:__l="__l"></tag-follow-button></view></view>