<view class="u-tabs data-v-d9072e9a" style="{{'background:'+(bgColor)+';'}}"><view class="data-v-d9072e9a"><scroll-view class="u-scroll-view data-v-d9072e9a" scroll-x="{{true}}" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}"><view class="{{['u-scroll-box','data-v-d9072e9a',(!isScroll)?'u-tabs-scroll-flex':'']}}" id="{{id}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-tab-item u-line-1 data-v-d9072e9a" style="{{item.s0}}" id="{{'u-tab-item-'+index}}" data-event-opts="{{[['tap',[['clickTab',[index]]]]]}}" bindtap="__e"><u-badge vue-id="{{'47dda29a-1-'+index}}" count="{{item.$orig[count]||item.$orig['count']||0}}" offset="{{offset}}" size="mini" class="data-v-d9072e9a" bind:__l="__l"></u-badge>{{''+(item.$orig[name]||item.$orig['name'])+''}}</view></block><block wx:if="{{showBar}}"><view class="u-tab-bar data-v-d9072e9a" style="{{$root.s1}}"></view></block></view></scroll-view></view></view>