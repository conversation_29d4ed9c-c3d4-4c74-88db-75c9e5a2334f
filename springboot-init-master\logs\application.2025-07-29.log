2025-07-29 09:14:09.812 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 27136 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 09:14:09.821 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 09:14:09.916 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-29 09:14:09.916 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-29 09:14:12.300 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-29 09:14:12.329 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.330 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.331 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.332 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:12.333 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 09:14:15.465 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 09:14:15.490 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 09:14:15.491 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 09:14:15.492 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 09:14:15.679 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:14:15.680 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5763 ms
2025-07-29 09:14:16.172 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@76555159'
2025-07-29 09:14:16.486 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 09:14:16.554 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 09:14:16.622 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 09:14:16.685 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 09:14:16.757 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 09:14:16.780 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 09:14:16.804 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 09:14:16.829 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 09:14:16.837 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 09:14:16.842 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 09:14:16.843 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 09:14:16.845 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 09:14:16.958 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 09:14:17.055 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 09:14:17.111 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 09:14:17.180 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 09:14:17.249 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 09:14:17.300 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 09:14:17.347 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 09:14:17.398 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 09:14:17.457 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 09:14:17.512 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 09:14:17.567 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 09:14:17.613 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 09:14:17.670 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 09:14:17.704 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 09:14:17.746 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 09:14:17.771 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 09:14:17.797 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 09:14:17.832 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 09:14:18.284 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 09:14:18.505 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 09:14:18.521 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 09:14:18.696 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 09:14:18.732 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 09:14:19.772 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 09:14:21.999 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 09:14:22.143 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 09:14:22.638 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 09:14:23.029 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 09:14:24.594 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 09:14:24.974 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 09:14:25.022 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 09:14:25.023 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 09:14:25.030 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 09:14:25.161 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 09:14:25.467 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 09:14:25.682 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 09:14:25.806 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 09:14:25.810 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 09:14:25.813 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 09:14:25.820 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 09:14:25.823 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 09:14:26.256 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 09:14:26.276 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 17.24 seconds (JVM running for 18.801)
2025-07-29 09:14:41.679 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 27136 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 09:14:41.680 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 09:14:42.267 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.267 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.267 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.269 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:14:42.270 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 09:14:42.583 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 09:14:42.585 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 09:14:42.587 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 09:14:42.631 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:14:42.631 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 946 ms
2025-07-29 09:14:42.849 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@6db8c913'
2025-07-29 09:14:42.889 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 09:14:42.916 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 09:14:42.952 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 09:14:42.984 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 09:14:43.012 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 09:14:43.020 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 09:14:43.024 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 09:14:43.033 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 09:14:43.035 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 09:14:43.038 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 09:14:43.038 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 09:14:43.039 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 09:14:43.067 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 09:14:43.097 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 09:14:43.118 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 09:14:43.144 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 09:14:43.163 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 09:14:43.188 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 09:14:43.219 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 09:14:43.250 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 09:14:43.274 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 09:14:43.295 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 09:14:43.326 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 09:14:43.362 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 09:14:43.401 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 09:14:43.428 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 09:14:43.453 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 09:14:43.478 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 09:14:43.500 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 09:14:43.520 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 09:14:43.752 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 09:14:43.863 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 09:14:43.872 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 09:14:43.994 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 09:14:44.003 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 09:14:44.309 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 09:14:45.355 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 09:14:45.417 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 09:14:45.544 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 09:14:45.634 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 09:14:45.691 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 09:14:45.798 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 09:14:45.799 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 09:14:45.800 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 09:14:45.835 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 09:14:45.883 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 09:14:45.957 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 09:14:46.004 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 09:14:46.005 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 09:14:46.007 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 09:14:46.009 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 09:14:46.011 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 09:14:46.172 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 09:14:46.173 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 4.562 seconds (JVM running for 38.698)
2025-07-29 09:14:46.176 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-29 09:45:06.378 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 27136 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 09:45:06.380 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.727 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.728 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.731 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.732 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.732 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.732 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 09:45:10.733 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 09:45:11.248 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 09:45:11.250 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 09:45:11.251 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 09:45:12.003 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:45:12.003 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5617 ms
2025-07-29 09:45:12.242 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@7f4f07e4'
2025-07-29 09:45:12.360 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 09:45:12.388 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 09:45:12.414 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 09:45:12.443 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 09:45:12.480 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 09:45:12.491 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 09:45:12.494 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 09:45:12.502 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 09:45:12.504 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 09:45:12.507 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 09:45:12.507 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 09:45:12.509 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 09:45:12.546 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 09:45:12.578 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 09:45:12.599 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 09:45:12.636 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 09:45:12.656 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 09:45:12.681 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 09:45:12.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 09:45:12.733 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 09:45:12.754 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 09:45:12.779 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 09:45:12.807 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 09:45:12.823 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 09:45:12.848 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 09:45:12.874 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 09:45:12.903 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 09:45:12.924 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 09:45:12.951 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 09:45:12.969 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 09:45:13.123 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 09:45:13.191 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 09:45:13.199 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 09:45:13.286 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 09:45:13.303 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 09:45:13.797 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 09:45:15.043 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 09:45:15.101 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 09:45:15.255 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 09:45:15.384 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 09:45:15.471 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 09:45:15.654 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 09:45:15.655 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 09:45:15.656 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 09:45:15.713 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 09:45:15.880 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 09:45:16.093 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 09:45:16.248 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 09:45:16.251 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 09:45:16.253 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 09:45:16.263 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 09:45:16.274 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 09:45:16.658 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 09:45:16.666 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 10.409 seconds (JVM running for 1869.19)
2025-07-29 09:45:16.675 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-29 14:27:50.902 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 33756 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 14:27:50.906 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 14:27:50.993 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-29 14:27:50.993 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-29 14:27:52.800 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-29 14:27:52.828 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.828 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.829 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.830 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.831 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.831 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.831 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.831 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.831 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.832 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.832 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.832 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 14:27:52.832 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 14:27:54.416 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 14:27:54.429 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 14:27:54.429 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 14:27:54.429 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 14:27:54.539 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 14:27:54.539 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3546 ms
2025-07-29 14:27:54.854 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1516f46c'
2025-07-29 14:27:55.089 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 14:27:55.140 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 14:27:55.187 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 14:27:55.233 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 14:27:55.271 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 14:27:55.288 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 14:27:55.297 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 14:27:55.312 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 14:27:55.317 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 14:27:55.320 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 14:27:55.321 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 14:27:55.323 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 14:27:55.375 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 14:27:55.425 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 14:27:55.462 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 14:27:55.517 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 14:27:55.546 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 14:27:55.579 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 14:27:55.624 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 14:27:55.662 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 14:27:55.695 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 14:27:55.730 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 14:27:55.763 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 14:27:55.784 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 14:27:55.805 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 14:27:55.824 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 14:27:55.844 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 14:27:55.867 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 14:27:55.883 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 14:27:55.906 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 14:27:56.146 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 14:27:56.268 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 14:27:56.278 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 14:27:56.369 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 14:27:56.394 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 14:27:56.987 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 14:27:58.253 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 14:27:58.316 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 14:27:58.487 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 14:27:58.629 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 14:27:58.825 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 14:27:58.996 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 14:27:59.024 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 14:27:59.025 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 14:27:59.027 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 14:27:59.099 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 14:27:59.271 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 14:27:59.405 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 14:27:59.480 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 14:27:59.481 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 14:27:59.483 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 14:27:59.484 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 14:27:59.486 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 14:27:59.744 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 14:27:59.756 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 9.556 seconds (JVM running for 11.085)
2025-07-29 14:28:12.016 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:28:12.016 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 14:28:12.019 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-29 14:28:12.164 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 1bfda769-66f5-46ce-9737-783d5764d350, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 14:28:12.206 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 14:28:12.856 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 14:28:12.885 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 14:28:12.920 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 14:28:12.992 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 14:28:13.003 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 14:28:13.004 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 14:28:13.011 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 1bfda769-66f5-46ce-9737-783d5764d350, cost: 889ms
2025-07-29 14:28:13.163 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 5ae2d9e7-e704-4142-ab1c-288d4a93659d, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 14:28:13.167 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 14:28:13.300 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 14:28:13.302 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 14:28:13.346 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 14:28:13.351 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 14:28:13.352 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 14:28:13.395 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.397 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.398 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.399 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.400 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.400 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.402 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.402 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.402 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.403 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.404 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.404 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.405 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.405 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.406 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.406 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.406 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.406 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.407 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:28:13.407 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:28:13.407 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 14:28:13.408 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 5ae2d9e7-e704-4142-ab1c-288d4a93659d, cost: 252ms
2025-07-29 14:28:14.654 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: a76b4632-6ec8-4ee6-996e-f5488644086b, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@4b1899fc]
2025-07-29 14:28:14.672 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 14:28:14.723 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 14:28:14.724 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 14:28:14.765 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 14:28:14.767 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 14:28:14.767 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 14:28:14.809 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 14:28:14.810 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 14:28:14.810 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 14:28:14.810 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 14:28:14.810 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: a76b4632-6ec8-4ee6-996e-f5488644086b, cost: 156ms
2025-07-29 14:31:58.962 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 70538600-2ec4-4c36-8ce0-cb9ecd8e727e, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 14:31:59.004 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 14:31:59.004 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 14:31:59.048 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 14:31:59.049 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 14:31:59.051 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 70538600-2ec4-4c36-8ce0-cb9ecd8e727e, cost: 89ms
2025-07-29 14:31:59.101 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 86df4e9f-581c-467c-81db-8ea4e041a01a, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 14:31:59.102 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 14:31:59.112 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 14:31:59.114 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 14:31:59.157 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 14:31:59.158 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 14:31:59.159 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 14:31:59.203 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.203 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.204 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.204 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.205 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.205 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.206 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.206 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.207 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.207 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.210 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.211 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.215 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.216 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.217 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.217 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.218 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.218 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.219 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:31:59.219 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:31:59.220 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 14:31:59.220 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 86df4e9f-581c-467c-81db-8ea4e041a01a, cost: 118ms
2025-07-29 14:32:01.519 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: f619ac99-efce-4666-b4d8-f3fb1f27546a, path: /api/post/detail, ip: 0:0:0:0:0:0:0:1, params: [52, 24840]
2025-07-29 14:32:01.522 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 79463d74-a6c7-46bc-a07a-367f8867a770, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-29 14:32:01.541 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: d3905443-bc7b-4b4c-9a05-98050ea22052, path: /api/comments/post/52, ip: 0:0:0:0:0:0:0:1, params: [52, 1, hot, 1, 10]
2025-07-29 14:32:01.546 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 获取帖子评论列表请求开始 - postId: 52, userId: 1, filter: hot, current: 1, pageSize: 10
2025-07-29 14:32:01.558 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务 - postId: 52, filter: hot, userId: 1, current: 1, pageSize: 10
2025-07-29 14:32:01.578 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 14:32:01.578 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 14:32:01.578 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 14:32:01.579 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 14:32:01.622 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 14:32:01.622 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 14:32:01.623 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.id = ? AND p.is_delete = 0
2025-07-29 14:32:01.624 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM comments WHERE is_delete = 0 AND (post_id = ? AND is_delete = ?)
2025-07-29 14:32:01.624 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostDetail - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 52(Long)
2025-07-29 14:32:01.625 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - ==> Parameters: 52(Long), 0(Integer)
2025-07-29 14:32:01.631 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-29 14:32:01.632 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 14:32:01.666 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 14:32:01.666 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 14:32:01.667 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.C.selectPage_mpCount - <==      Total: 1
2025-07-29 14:32:01.667 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostDetail - <==      Total: 1
2025-07-29 14:32:01.668 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.PostMapper.selectById - ==>  Preparing: SELECT id,user_id,title,content,images,cover_image,location_name,location_latitude,location_longitude,location_address,like_count,comment_count,share_count,view_count,is_public,status,tags,create_time,update_time,is_delete FROM posts WHERE id=? AND is_delete=0
2025-07-29 14:32:01.668 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.CommentServiceImpl - 获取帖子评论列表服务完成 - 总评论数: 0
2025-07-29 14:32:01.669 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.PostMapper.selectById - ==> Parameters: 52(Long)
2025-07-29 14:32:01.669 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.CommentController - 🎯 帖子评论分页查询成功 - postId: 52, 总数: 0, 当前页: 1/0, 返回数: 0, hasMore: false
2025-07-29 14:32:01.669 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: d3905443-bc7b-4b4c-9a05-98050ea22052, cost: 128ms
2025-07-29 14:32:01.677 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-29 14:32:01.677 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-29 14:32:01.677 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 79463d74-a6c7-46bc-a07a-367f8867a770, cost: 155ms
2025-07-29 14:32:01.717 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.PostMapper.selectById - <==      Total: 1
2025-07-29 14:32:01.719 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - ==>  Preparing: UPDATE posts SET view_count = view_count + 1 WHERE id = ?
2025-07-29 14:32:01.719 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - ==> Parameters: 52(Long)
2025-07-29 14:32:01.801 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - <==    Updates: 1
2025-07-29 14:32:01.803 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - ==>  Preparing: INSERT INTO post_stats (post_id, view_count, last_activity_time) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE view_count = view_count + ?, last_activity_time = NOW()
2025-07-29 14:32:01.804 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - ==> Parameters: 52(Long), 1(Integer), 1(Integer)
2025-07-29 14:32:01.887 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.incrementViewCount - <==    Updates: 2
2025-07-29 14:32:01.887 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: f619ac99-efce-4666-b4d8-f3fb1f27546a, cost: 368ms
2025-07-29 16:39:53.185 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-29 16:39:53.193 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-29 16:39:53.387 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 33756 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 16:39:53.387 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 16:39:54.856 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.856 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.856 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.856 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.857 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.857 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.857 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.857 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.858 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.859 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.860 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.860 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.860 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.860 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.860 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 16:39:54.861 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 16:39:55.545 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 16:39:55.547 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 16:39:55.547 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 16:39:55.547 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 16:39:55.577 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 16:39:55.577 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2183 ms
2025-07-29 16:39:55.709 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@43e3df38'
2025-07-29 16:39:55.742 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 16:39:55.758 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 16:39:55.781 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 16:39:55.863 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 16:39:55.889 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 16:39:55.899 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 16:39:55.902 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 16:39:55.912 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 16:39:55.913 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 16:39:55.915 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 16:39:55.915 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 16:39:55.916 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 16:39:55.938 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 16:39:55.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 16:39:55.975 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 16:39:56.026 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 16:39:56.059 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 16:39:56.094 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 16:39:56.112 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 16:39:56.131 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 16:39:56.151 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 16:39:56.168 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 16:39:56.193 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 16:39:56.210 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 16:39:56.232 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 16:39:56.251 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 16:39:56.271 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 16:39:56.285 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 16:39:56.308 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 16:39:56.345 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 16:39:56.621 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 16:39:56.688 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 16:39:56.694 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 16:39:56.780 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 16:39:56.801 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 16:39:57.217 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 16:39:58.454 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 16:39:58.514 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 16:39:58.618 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 16:39:58.717 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 16:39:58.808 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 16:39:58.950 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 16:39:58.953 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 16:39:58.954 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 16:39:58.955 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 16:39:59.002 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 16:39:59.087 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 16:39:59.243 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 16:39:59.328 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 16:39:59.331 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 16:39:59.333 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 16:39:59.335 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 16:39:59.337 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 16:39:59.570 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 16:39:59.573 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 6.265 seconds (JVM running for 7930.902)
2025-07-29 16:39:59.577 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-29 17:26:38.040 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 17:26:38.042 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 17:26:38.047 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-29 17:26:38.067 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 994c7afe-5000-46e0-b8d3-6685111ed5c4, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:26:38.098 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-29 17:26:38.570 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-29 17:26:38.574 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:26:38.576 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:26:38.643 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:26:38.644 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:26:38.660 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 994c7afe-5000-46e0-b8d3-6685111ed5c4, cost: 600ms
2025-07-29 17:26:38.769 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: a586812a-a66c-49db-b3cc-76066b293ac6, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:26:38.777 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:26:38.810 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:26:38.811 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:26:38.858 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:26:38.861 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:26:38.863 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:26:38.910 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.912 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.912 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.913 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.913 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.913 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.913 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.914 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.914 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.914 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.915 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.916 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.916 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.916 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:26:38.916 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:26:38.917 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:26:38.917 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: a586812a-a66c-49db-b3cc-76066b293ac6, cost: 164ms
2025-07-29 17:26:41.436 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: b44a8af2-3bf3-4cbe-a373-d429a2962056, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@26f15f59]
2025-07-29 17:26:41.475 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:26:41.526 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:26:41.527 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:26:41.573 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:26:41.574 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:26:41.575 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:26:41.622 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:26:41.624 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:26:41.627 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:26:41.627 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:26:41.627 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: b44a8af2-3bf3-4cbe-a373-d429a2962056, cost: 191ms
2025-07-29 17:27:12.365 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 3a36ccee-4390-46f5-8b02-1cdcd53453f2, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:27:12.410 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:27:12.411 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:27:12.456 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:27:12.457 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 3a36ccee-4390-46f5-8b02-1cdcd53453f2, cost: 91ms
2025-07-29 17:27:12.527 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 66b87c19-3df7-4c1a-b0d1-64aca729e5bc, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:27:12.528 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:27:12.537 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:27:12.537 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:27:12.587 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:27:12.588 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:27:12.589 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:27:12.638 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.639 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.640 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.640 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.641 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.642 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.642 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.646 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.647 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.652 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.652 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.653 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.653 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.654 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.654 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.654 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:27:12.655 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:27:12.655 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:27:12.655 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 66b87c19-3df7-4c1a-b0d1-64aca729e5bc, cost: 128ms
2025-07-29 17:27:15.999 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 3c10645d-21bd-4809-bcfd-cd616af4fe39, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@26f15f59]
2025-07-29 17:27:15.999 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:27:16.051 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:27:16.053 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:27:16.100 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:27:16.102 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:27:16.104 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:27:16.153 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:27:16.153 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:27:16.153 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:27:16.154 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:27:16.154 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 3c10645d-21bd-4809-bcfd-cd616af4fe39, cost: 155ms
2025-07-29 17:27:19.843 [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-07-29 17:27:19.848 [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2025-07-29 17:27:20.078 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 33756 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 17:27:20.079 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 17:27:21.118 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.118 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.119 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:21.120 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 17:27:21.197 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Unable to load cache item
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:79)
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:134)
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:319)
	at org.springframework.cglib.proxy.Enhancer.createHelper(Enhancer.java:572)
	at org.springframework.cglib.proxy.Enhancer.createClass(Enhancer.java:419)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:137)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:109)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:447)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:268)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:325)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:147)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.NoClassDefFoundError: com/yupi/springbootinit/websocket/ChatWebSocketHandler
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3373)
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2555)
	at org.springframework.cglib.proxy.Enhancer.generateClass(Enhancer.java:661)
	at org.springframework.cglib.transform.TransformingClassGenerator.generateClass(TransformingClassGenerator.java:33)
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:25)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.generate(ClassLoaderAwareGeneratorStrategy.java:57)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:358)
	at org.springframework.cglib.proxy.Enhancer.generate(Enhancer.java:585)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:110)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:108)
	at org.springframework.cglib.core.internal.LoadingCache$2.call(LoadingCache.java:54)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:61)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.yupi.springbootinit.websocket.ChatWebSocketHandler
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:145)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 39 common frames omitted
2025-07-29 17:27:24.992 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-29 17:27:24.993 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-29 17:27:25.001 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 33756 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 17:27:25.001 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.909 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:27:25.910 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 17:27:26.137 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 17:27:26.138 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 17:27:26.138 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 17:27:26.138 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 17:27:26.172 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 17:27:26.172 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1167 ms
2025-07-29 17:27:26.319 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4746b107'
2025-07-29 17:27:26.346 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 17:27:26.373 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 17:27:26.392 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 17:27:26.411 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 17:27:26.430 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 17:27:26.436 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 17:27:26.446 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 17:27:26.454 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 17:27:26.456 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 17:27:26.457 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 17:27:26.457 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 17:27:26.458 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 17:27:26.478 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 17:27:26.496 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 17:27:26.511 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 17:27:26.547 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 17:27:26.569 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 17:27:26.595 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 17:27:26.616 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 17:27:26.649 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 17:27:26.739 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 17:27:26.782 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 17:27:26.845 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 17:27:26.881 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 17:27:26.945 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 17:27:26.972 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 17:27:27.001 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 17:27:27.029 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 17:27:27.051 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 17:27:27.075 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 17:27:27.247 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 17:27:27.309 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 17:27:27.317 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 17:27:27.397 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 17:27:27.413 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 17:27:27.877 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 17:27:28.976 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 17:27:29.040 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 17:27:29.150 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 17:27:29.255 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 17:27:29.338 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 17:27:29.564 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 17:27:29.569 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 17:27:29.570 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 17:27:29.571 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 17:27:29.620 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 17:27:29.720 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 17:27:29.878 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 17:27:30.027 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 17:27:30.032 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 17:27:30.045 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 17:27:30.050 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 17:27:30.068 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 17:27:30.436 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 17:27:30.438 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 5.476 seconds (JVM running for 10781.767)
2025-07-29 17:27:30.443 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-29 17:34:10.757 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 17:34:10.758 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 17:34:10.761 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-29 17:34:10.768 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 3c3942f0-fd25-49cf-bc51-65356c37fe1f, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:34:10.795 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-07-29 17:34:11.253 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-07-29 17:34:11.253 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:34:11.253 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:34:11.299 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:34:11.299 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:34:11.299 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:34:11.300 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:34:11.308 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 3c3942f0-fd25-49cf-bc51-65356c37fe1f, cost: 542ms
2025-07-29 17:34:11.379 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: e8fabefb-788b-44d6-948c-a3c874bea0d1, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:34:11.387 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:34:11.416 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:34:11.417 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:34:11.463 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:34:11.465 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:34:11.466 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:34:11.512 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.513 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.514 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.514 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.515 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.515 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.516 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.516 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.516 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.516 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.517 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.517 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.517 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.518 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.518 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.518 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.519 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.519 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.519 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:34:11.519 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:34:11.519 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:34:11.520 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: e8fabefb-788b-44d6-948c-a3c874bea0d1, cost: 153ms
2025-07-29 17:34:12.623 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: c0943b25-8d9c-4f05-b48f-b29625e4a313, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@5a3585ff]
2025-07-29 17:34:12.666 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:34:12.728 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:34:12.729 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:34:12.775 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:34:12.776 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:34:12.777 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:34:12.822 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:34:12.822 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:34:12.824 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:34:12.824 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:34:12.824 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: c0943b25-8d9c-4f05-b48f-b29625e4a313, cost: 202ms
2025-07-29 17:38:54.818 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: ebb45b2c-6246-4771-bbc8-866a5a0ef36b, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:38:54.868 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:38:54.868 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:38:54.921 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:38:54.923 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: ebb45b2c-6246-4771-bbc8-866a5a0ef36b, cost: 105ms
2025-07-29 17:38:55.145 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 2b02878b-a1de-496c-9b8b-349919e08dac, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:38:55.146 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:38:55.153 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:38:55.153 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:38:55.199 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:38:55.200 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:38:55.201 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:38:55.246 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.247 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.248 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.249 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.249 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.250 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.251 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.253 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.254 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.254 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.254 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.254 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.254 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.255 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:38:55.255 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:38:55.255 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:38:55.255 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 2b02878b-a1de-496c-9b8b-349919e08dac, cost: 109ms
2025-07-29 17:38:56.759 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 0b516b33-17b4-4088-82aa-c8d445da7b78, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@5a3585ff]
2025-07-29 17:38:56.759 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:38:56.809 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:38:56.810 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:38:56.876 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:38:56.877 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:38:56.878 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:38:56.948 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:38:56.948 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:38:56.948 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:38:56.948 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:38:56.948 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 0b516b33-17b4-4088-82aa-c8d445da7b78, cost: 189ms
2025-07-29 17:39:42.863 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 2618071f-3b3f-4520-b4cc-3310405410e8, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:39:42.909 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:39:42.910 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:39:42.957 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:39:42.958 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 2618071f-3b3f-4520-b4cc-3310405410e8, cost: 95ms
2025-07-29 17:39:43.013 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: b386520b-a05a-4403-9af8-c0a6d049ad35, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:39:43.013 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:39:43.020 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:39:43.021 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:39:43.067 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:39:43.069 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:39:43.070 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:39:43.116 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.117 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.117 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.117 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.118 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.118 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.118 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.118 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.119 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.119 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.120 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.120 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.120 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.120 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.120 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.121 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.121 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.121 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.121 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:39:43.121 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:39:43.122 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:39:43.122 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: b386520b-a05a-4403-9af8-c0a6d049ad35, cost: 109ms
2025-07-29 17:39:50.968 [Thread-13] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Shutdown initiated...
2025-07-29 17:39:50.972 [Thread-13] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Shutdown completed.
2025-07-29 17:39:51.165 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 33756 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-29 17:39:51.165 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.962 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-29 17:39:51.963 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-29 17:39:52.126 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-29 17:39:52.127 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 17:39:52.127 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 17:39:52.127 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-29 17:39:52.157 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-29 17:39:52.157 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 987 ms
2025-07-29 17:39:52.473 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1d1144c1'
2025-07-29 17:39:52.549 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-29 17:39:52.586 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-29 17:39:52.619 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-29 17:39:52.661 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-29 17:39:52.688 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-29 17:39:52.701 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 17:39:52.704 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 17:39:52.717 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-29 17:39:52.722 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 17:39:52.724 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-29 17:39:52.724 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 17:39:52.726 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-29 17:39:52.752 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-29 17:39:52.855 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-29 17:39:53.150 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-29 17:39:53.180 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-29 17:39:53.203 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-29 17:39:53.230 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-29 17:39:53.248 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-29 17:39:53.266 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-29 17:39:53.287 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-29 17:39:53.306 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-29 17:39:53.334 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-29 17:39:53.361 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-29 17:39:53.384 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-29 17:39:53.405 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-29 17:39:53.439 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-29 17:39:53.455 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-29 17:39:53.468 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-29 17:39:53.482 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-29 17:39:53.598 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-29 17:39:53.676 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-29 17:39:53.683 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-29 17:39:53.763 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-29 17:39:53.777 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-29 17:39:54.106 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-29 17:39:55.147 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-29 17:39:55.192 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-29 17:39:55.304 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-29 17:39:55.402 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 17:39:55.478 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-29 17:39:55.601 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-29 17:39:55.605 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-29 17:39:55.605 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-29 17:39:55.606 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-29 17:39:55.634 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-29 17:39:55.688 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-29 17:39:55.751 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-29 17:39:55.802 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-29 17:39:55.803 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-29 17:39:55.804 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-29 17:39:55.805 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-29 17:39:55.806 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-29 17:39:55.971 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-29 17:39:55.973 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 4.868 seconds (JVM running for 11527.301)
2025-07-29 17:39:55.975 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-29 17:40:08.911 [http-nio-0.0.0.0-8101-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 17:40:08.911 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 17:40:08.914 [http-nio-0.0.0.0-8101-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-29 17:40:08.918 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: d94b93bc-95e1-451d-b51e-1ed63c5782bc, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:40:08.935 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Starting...
2025-07-29 17:40:09.437 [http-nio-0.0.0.0-8101-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Start completed.
2025-07-29 17:40:09.438 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:40:09.438 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:40:09.486 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:40:09.487 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:40:09.511 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: d94b93bc-95e1-451d-b51e-1ed63c5782bc, cost: 594ms
2025-07-29 17:40:09.576 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 865dcd76-00a6-476c-b2ac-0bda0eafd5cc, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:40:09.584 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:40:09.600 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:40:09.601 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:40:09.650 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:40:09.651 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:40:09.652 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:40:09.701 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.702 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.702 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.702 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.703 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.703 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.703 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.703 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.703 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.704 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.704 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.704 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.705 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.706 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:40:09.706 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:40:09.706 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:40:09.707 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 865dcd76-00a6-476c-b2ac-0bda0eafd5cc, cost: 139ms
2025-07-29 17:40:11.067 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 3b11a7bf-1945-4694-b4b2-a6d1a199b76f, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:40:11.090 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:40:11.177 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:40:11.178 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:40:11.228 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:40:11.230 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:40:11.230 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:40:11.282 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:40:11.282 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:40:11.284 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:40:11.284 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:40:11.284 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 3b11a7bf-1945-4694-b4b2-a6d1a199b76f, cost: 217ms
2025-07-29 17:40:13.845 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: f72d76e1-3da7-43f5-8b5e-2eed0409194b, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:40:13.914 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-29 17:40:13.915 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-29 17:40:13.962 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-29 17:40:13.963 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-29 17:40:13.964 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-29 17:40:14.015 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 7
2025-07-29 17:40:14.029 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.029 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.077 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.078 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.078 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.127 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.128 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.128 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.178 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.179 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.179 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.227 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.228 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.228 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.274 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.275 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.276 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.322 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.323 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:40:14.323 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:40:14.369 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:40:14.369 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 7
2025-07-29 17:40:14.370 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: f72d76e1-3da7-43f5-8b5e-2eed0409194b, cost: 524ms
2025-07-29 17:40:14.416 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: fa376319-264b-4f38-9a02-0de9ea488aa5, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:40:14.416 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: fa376319-264b-4f38-9a02-0de9ea488aa5, cost: 0ms
2025-07-29 17:46:19.159 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 5cdf864e-6e9d-43db-9608-66f5198f34b5, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:46:19.214 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:46:19.215 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:46:19.264 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:46:19.265 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:46:19.266 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 5cdf864e-6e9d-43db-9608-66f5198f34b5, cost: 106ms
2025-07-29 17:46:19.336 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 4d7adfaf-9d2c-4a84-97a7-2fab7983fd7a, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:46:19.336 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:46:19.343 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:46:19.344 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:46:19.391 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:46:19.393 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:46:19.394 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:46:19.442 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.443 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.443 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.443 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.444 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.444 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.444 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.444 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.445 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.445 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.448 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.448 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.452 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.453 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.453 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.454 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.454 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.454 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.455 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:46:19.455 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:46:19.455 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:46:19.456 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 4d7adfaf-9d2c-4a84-97a7-2fab7983fd7a, cost: 120ms
2025-07-29 17:46:20.813 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 2c607d98-79b9-4714-a826-a88c10fe6435, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:46:20.814 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:46:20.864 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:46:20.865 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:46:20.919 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:46:20.920 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:46:20.921 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:46:20.969 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:46:20.970 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:46:20.970 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:46:20.970 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:46:20.970 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 2c607d98-79b9-4714-a826-a88c10fe6435, cost: 156ms
2025-07-29 17:46:23.531 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 2825c255-85b1-4e97-a0aa-4cf3f40db62d, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:46:23.586 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-29 17:46:23.587 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-29 17:46:23.638 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-29 17:46:23.640 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-29 17:46:23.640 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-29 17:46:23.690 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 7
2025-07-29 17:46:23.691 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.692 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:23.740 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:23.741 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.742 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:23.790 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:23.791 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.792 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:23.840 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:23.840 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.841 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:23.895 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:23.896 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.897 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:23.945 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:23.946 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:23.946 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:24.005 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:24.007 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:46:24.007 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:46:24.055 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:46:24.056 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 7
2025-07-29 17:46:24.056 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 2825c255-85b1-4e97-a0aa-4cf3f40db62d, cost: 525ms
2025-07-29 17:46:24.114 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 0628b056-d735-484a-91cd-32bdce1f4635, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:46:24.114 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 0628b056-d735-484a-91cd-32bdce1f4635, cost: 0ms
2025-07-29 17:50:41.253 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: f79bedfa-94aa-42f4-8044-55aa6fd23c23, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 17:50:41.299 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 17:50:41.300 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 17:50:41.353 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 17:50:41.354 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: f79bedfa-94aa-42f4-8044-55aa6fd23c23, cost: 102ms
2025-07-29 17:50:41.557 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 3da02858-6709-4e4a-9e57-05dafee972bc, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 17:50:41.557 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 17:50:41.564 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 17:50:41.565 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 17:50:41.617 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 17:50:41.617 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 17:50:41.619 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 17:50:41.666 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.667 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.667 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.667 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.667 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.668 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 17:50:41.669 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 17:50:41.670 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 17:50:41.670 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 3da02858-6709-4e4a-9e57-05dafee972bc, cost: 113ms
2025-07-29 17:50:43.461 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: efc330ec-6c3a-4803-a673-0a780c887fa9, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:50:43.461 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 17:50:43.510 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 17:50:43.510 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 17:50:43.560 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 17:50:43.560 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 17:50:43.561 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 17:50:43.609 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 17:50:43.609 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 17:50:43.609 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 17:50:43.609 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 17:50:43.609 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: efc330ec-6c3a-4803-a673-0a780c887fa9, cost: 147ms
2025-07-29 17:50:46.013 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: ab0a8d67-0f71-42af-a43c-acd704875022, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:50:46.076 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-29 17:50:46.076 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-29 17:50:46.145 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-29 17:50:46.146 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-29 17:50:46.147 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-29 17:50:46.198 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 7
2025-07-29 17:50:46.199 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.199 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.245 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.245 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.246 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.299 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.300 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.300 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.348 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.349 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.349 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.396 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.397 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.397 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.494 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.495 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.495 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.542 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.543 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 17:50:46.544 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 17:50:46.594 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 17:50:46.594 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 7
2025-07-29 17:50:46.594 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: ab0a8d67-0f71-42af-a43c-acd704875022, cost: 580ms
2025-07-29 17:50:46.806 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 12a44634-0a23-463d-bf06-5a5fc6a0bc1a, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 17:50:46.806 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 12a44634-0a23-463d-bf06-5a5fc6a0bc1a, cost: 0ms
2025-07-29 18:03:09.176 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 3aa98602-99d0-4205-a430-70bf0ec1f6c3, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-29 18:03:09.225 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-29 18:03:09.227 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-29 18:03:09.274 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-29 18:03:09.274 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-29 18:03:09.274 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-29 18:03:09.274 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-29 18:03:09.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-29 18:03:09.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-29 18:03:09.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 18:03:09.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-29 18:03:09.275 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-29 18:03:09.277 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 3aa98602-99d0-4205-a430-70bf0ec1f6c3, cost: 101ms
2025-07-29 18:03:09.343 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 88b4d78d-c388-4286-b23e-4cefd04e5427, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-29 18:03:09.344 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-29 18:03:09.347 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-29 18:03:09.348 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-29 18:03:09.395 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-29 18:03:09.397 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-29 18:03:09.398 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-29 18:03:09.449 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.449 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.449 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.449 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.450 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.451 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.452 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-29 18:03:09.452 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-29 18:03:09.452 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-29 18:03:09.452 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 88b4d78d-c388-4286-b23e-4cefd04e5427, cost: 108ms
2025-07-29 18:03:10.590 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: f4c1d004-a818-4efc-90cf-b0afec57f4e0, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 18:03:10.590 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-29 18:03:10.640 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-29 18:03:10.641 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-29 18:03:10.688 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-29 18:03:10.689 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-29 18:03:10.689 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-29 18:03:10.737 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-29 18:03:10.737 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-29 18:03:10.737 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-29 18:03:10.737 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-29 18:03:10.737 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: f4c1d004-a818-4efc-90cf-b0afec57f4e0, cost: 147ms
2025-07-29 18:03:13.085 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 96643957-5e4a-4657-874a-bca4c488b20d, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 18:03:13.139 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-29 18:03:13.139 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-29 18:03:13.185 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-29 18:03:13.186 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-29 18:03:13.187 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-29 18:03:13.233 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 7
2025-07-29 18:03:13.234 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.235 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.282 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.283 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.283 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.336 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.337 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.337 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.392 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.392 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.393 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.444 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.445 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.445 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.493 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.493 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.494 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.545 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.545 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-29 18:03:13.546 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-29 18:03:13.592 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-29 18:03:13.593 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 7
2025-07-29 18:03:13.593 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 96643957-5e4a-4657-874a-bca4c488b20d, cost: 507ms
2025-07-29 18:03:13.685 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 750fdd0d-58c6-4bcd-9588-550118150a73, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@6997469a]
2025-07-29 18:03:13.685 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 750fdd0d-58c6-4bcd-9588-550118150a73, cost: 0ms
2025-07-29 21:18:19.117 [HikariPool-4 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-4 - Thread starvation or clock leap detected (housekeeper delta=3h10m39s110ms93µs600ns).
2025-07-29 22:53:16.642 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Shutdown initiated...
2025-07-29 22:53:20.251 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-4 - Shutdown completed.
