@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.u-fixed-placeholder.data-v-627f7c73 {
  box-sizing: content-box;
}
.u-tabbar__content.data-v-627f7c73 {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 998;
  box-sizing: content-box;
}
.u-tabbar__content__circle__border.data-v-627f7c73 {
  border-radius: 100%;
  width: 110rpx;
  height: 110rpx;
  top: -48rpx;
  position: absolute;
  z-index: 4;
  background-color: #ffffff;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.u-tabbar__content__circle__border.data-v-627f7c73:after {
  border-radius: 100px;
}
.u-tabbar__content__item.data-v-627f7c73 {
  flex: 1;
  justify-content: center;
  height: 100%;
  padding: 12rpx 0;
  display: flex;
  flex-direction: row;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.u-tabbar__content__item__button.data-v-627f7c73 {
  position: absolute;
  top: 14rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.u-tabbar__content__item__text.data-v-627f7c73 {
  color: #606266;
  font-size: 26rpx;
  line-height: 28rpx;
  position: absolute;
  bottom: 14rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 100%;
  text-align: center;
}
.u-tabbar__content__circle.data-v-627f7c73 {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-direction: column;
  justify-content: space-between;
  z-index: 10;
  height: calc(100% - 1px);
}
.u-tabbar__content__circle__button.data-v-627f7c73 {
  width: 90rpx;
  height: 90rpx;
  border-radius: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: #ffffff;
  top: -40rpx;
  left: 50%;
  z-index: 6;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

