<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostTagMapper">

    <!-- 根据帖子ID查询标签ID列表 -->
    <select id="selectTagIdsByPostId" resultType="java.lang.Long">
        SELECT tag_id FROM post_tags 
        WHERE post_id = #{postId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据标签ID查询帖子ID列表 -->
    <select id="selectPostIdsByTagId" resultType="java.lang.Long">
        SELECT post_id FROM post_tags 
        WHERE tag_id = #{tagId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据帖子ID删除所有标签关联 -->
    <delete id="deleteByPostId">
        DELETE FROM post_tags WHERE post_id = #{postId}
    </delete>

    <!-- 根据标签ID删除所有帖子关联 -->
    <delete id="deleteByTagId">
        DELETE FROM post_tags WHERE tag_id = #{tagId}
    </delete>

    <!-- 批量插入帖子标签关联 -->
    <insert id="batchInsert">
        INSERT INTO post_tags (post_id, tag_id, create_time)
        VALUES
        <foreach collection="postTags" item="postTag" separator=",">
            (#{postTag.postId}, #{postTag.tagId}, NOW())
        </foreach>
    </insert>

    <!-- 查询帖子的标签名称列表 -->
    <select id="selectTagNamesByPostId" resultType="java.lang.String">
        SELECT t.name FROM post_tags pt
        INNER JOIN tags t ON pt.tag_id = t.id
        WHERE pt.post_id = #{postId} AND t.is_delete = 0
        ORDER BY pt.create_time ASC
    </select>

    <!-- 批量查询多个帖子的标签 -->
    <select id="selectByPostIds" resultType="com.yupi.springbootinit.model.entity.PostTag">
        SELECT pt.*, t.name as tag_name FROM post_tags pt
        INNER JOIN tags t ON pt.tag_id = t.id
        WHERE pt.post_id IN
        <foreach collection="postIds" item="postId" open="(" separator="," close=")">
            #{postId}
        </foreach>
        AND t.is_delete = 0
        ORDER BY pt.post_id, pt.create_time ASC
    </select>

</mapper>
