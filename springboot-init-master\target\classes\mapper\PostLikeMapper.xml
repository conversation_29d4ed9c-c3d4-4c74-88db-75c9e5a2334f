<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostLikeMapper">

    <!-- 查询用户是否已点赞帖子（包括已删除的记录） -->
    <select id="selectByPostIdAndUserId" resultType="com.yupi.springbootinit.model.entity.PostLike">
        SELECT * FROM post_likes
        WHERE post_id = #{postId} AND user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 统计帖子点赞数（只统计未删除的记录） -->
    <select id="countByPostId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_likes
        WHERE post_id = #{postId} AND is_delete = 0
    </select>

    <!-- 统计用户点赞数（只统计未删除的记录） -->
    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM post_likes
        WHERE user_id = #{userId} AND is_delete = 0
    </select>

    <!-- 批量查询用户对帖子的点赞状态（只查询未删除的记录） -->
    <select id="selectBatchByPostIds" resultType="com.yupi.springbootinit.model.entity.PostLike">
        SELECT * FROM post_likes
        WHERE user_id = #{userId} AND is_delete = 0
        AND post_id IN
        <foreach collection="postIds" item="postId" open="(" separator="," close=")">
            #{postId}
        </foreach>
        ORDER BY create_time DESC
    </select>

</mapper>
