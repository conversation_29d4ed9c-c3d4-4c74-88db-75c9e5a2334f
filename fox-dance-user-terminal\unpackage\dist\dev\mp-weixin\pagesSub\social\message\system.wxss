@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.system-message-container.data-v-65989434 {
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}
.read-all-section.data-v-65989434 {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #e4e7ed;
}
.read-all-btn.data-v-65989434 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72rpx;
  background: #f8f9fa;
  border: 2rpx solid #e4e7ed;
  border-radius: 36rpx;
  transition: all 0.2s ease;
}
.read-all-btn.data-v-65989434:active {
  background: #e9ecef;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.read-all-text.data-v-65989434 {
  font-size: 28rpx;
  color: #2979ff;
  margin-left: 12rpx;
  font-weight: 500;
}
.message-list.data-v-65989434 {
  flex: 1;
  padding: 32rpx 0;
}
.message-card.data-v-65989434 {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.message-icon.data-v-65989434 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.icon-system.data-v-65989434 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.icon-activity.data-v-65989434 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.icon-security.data-v-65989434 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.message-content.data-v-65989434 {
  flex: 1;
}
.message-header.data-v-65989434 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.message-title.data-v-65989434 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
}
.message-time.data-v-65989434 {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}
.message-desc.data-v-65989434 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}
.unread-dot.data-v-65989434 {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}
.empty-state.data-v-65989434 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.empty-image.data-v-65989434 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}
.empty-text.data-v-65989434 {
  font-size: 28rpx;
  color: #999;
}
.load-more.data-v-65989434 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}
.load-text.data-v-65989434 {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

