<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.SystemConfigMapper">

    <!-- 根据配置键查询配置 -->
    <select id="selectByConfigKey" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE config_key = #{configKey}
        LIMIT 1
    </select>

    <!-- 批量根据配置键查询配置 -->
    <select id="selectByConfigKeys" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE config_key IN
        <foreach collection="configKeys" item="configKey" open="(" separator="," close=")">
            #{configKey}
        </foreach>
    </select>

    <!-- 查询所有公开配置 -->
    <select id="selectPublicConfigs" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE is_public = 1
        ORDER BY config_key ASC
    </select>

    <!-- 查询所有私有配置 -->
    <select id="selectPrivateConfigs" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE is_public = 0
        ORDER BY config_key ASC
    </select>

    <!-- 根据配置类型查询配置 -->
    <select id="selectByConfigType" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE config_type = #{configType}
        ORDER BY config_key ASC
    </select>

    <!-- 更新配置值 -->
    <update id="updateConfigValue">
        UPDATE system_configs 
        SET config_value = #{configValue}, update_time = NOW()
        WHERE config_key = #{configKey}
    </update>

    <!-- 批量更新配置 -->
    <update id="batchUpdateConfigs">
        <foreach collection="configs" item="value" index="key" separator=";">
            UPDATE system_configs 
            SET config_value = #{value}, update_time = NOW()
            WHERE config_key = #{key}
        </foreach>
    </update>

    <!-- 搜索配置（根据键或描述模糊匹配） -->
    <select id="searchConfigs" resultType="com.yupi.springbootinit.model.entity.SystemConfig">
        SELECT * FROM system_configs 
        WHERE config_key LIKE CONCAT('%', #{keyword}, '%')
           OR description LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY config_key ASC
    </select>

</mapper>
