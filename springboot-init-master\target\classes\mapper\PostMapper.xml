<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PostMapper">

    <resultMap id="PostVOResultMap" type="com.yupi.springbootinit.model.vo.PostVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="images" column="images" jdbcType="VARCHAR" typeHandler="com.yupi.springbootinit.config.ListJsonTypeHandler"/>
        <result property="coverImage" column="cover_image" jdbcType="VARCHAR"/>
        <result property="locationName" column="location_name" jdbcType="VARCHAR"/>
        <result property="locationLatitude" column="location_latitude" jdbcType="DECIMAL"/>
        <result property="locationLongitude" column="location_longitude" jdbcType="DECIMAL"/>
        <result property="locationAddress" column="location_address" jdbcType="VARCHAR"/>
        <result property="likeCount" column="like_count" jdbcType="INTEGER"/>
        <result property="commentCount" column="comment_count" jdbcType="INTEGER"/>
        <result property="shareCount" column="share_count" jdbcType="INTEGER"/>
        <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
        <result property="isPublic" column="is_public" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isLiked" column="is_liked" jdbcType="BOOLEAN"/>
        <result property="isFavorited" column="is_favorited" jdbcType="BOOLEAN"/>
        <result property="isOwner" column="is_owner" jdbcType="BOOLEAN"/>
        <result property="distance" column="distance" jdbcType="DOUBLE"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude,
        p.location_longitude, p.location_address, p.like_count, p.comment_count,
        p.share_count, p.view_count, p.is_public, p.status, p.create_time,
        u.nickname, u.avatar, u.level
    </sql>

    <!-- 用户互动状态查询 -->
    <sql id="User_Interaction_Status">
        CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
        CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited,
        CASE WHEN p.user_id = #{currentUserId} THEN 1 ELSE 0 END as is_owner
    </sql>

    <!-- 分页查询帖子列表 -->
    <select id="selectPostPage" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        <where>
            p.is_delete = 0
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
            <if test="query.status == null">
                AND p.status = 1
            </if>
            <if test="query.isPublic != null">
                AND p.is_public = #{query.isPublic}
            </if>
            <if test="query.isPublic == null and (query.userId == null or currentUserId != query.userId)">
                AND p.is_public = 1
            </if>
            <if test="query.userId != null">
                AND p.user_id = #{query.userId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND p.content LIKE CONCAT('%', #{query.keyword}, '%')
            </if>
            <if test="query.startTime != null">
                AND p.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND p.create_time &lt;= #{query.endTime}
            </if>
            <if test="query.tags != null and query.tags.size() > 0">
                AND (
                    <foreach collection="query.tags" item="tag" separator=" OR ">
                        p.tags LIKE CONCAT('%', #{tag}, '%')
                    </foreach>
                )
            </if>
        </where>
        <choose>
            <when test="query.sortField == 'like_count'">
                ORDER BY p.like_count ${query.sortOrder}, p.create_time DESC
            </when>
            <when test="query.sortField == 'comment_count'">
                ORDER BY p.comment_count ${query.sortOrder}, p.create_time DESC
            </when>
            <when test="query.sortField == 'view_count'">
                ORDER BY p.view_count ${query.sortOrder}, p.create_time DESC
            </when>
            <otherwise>
                ORDER BY p.create_time ${query.sortOrder}
            </otherwise>
        </choose>
    </select>

    <!-- 查询帖子详情 -->
    <select id="selectPostDetail" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.id = #{postId} AND p.is_delete = 0
    </select>

    <!-- 查询用户的帖子列表 -->
    <select id="selectUserPosts" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.user_id = #{userId} AND p.is_delete = 0
        <if test="status != null">
            AND p.status = #{status}
        </if>
        <if test="currentUserId != userId">
            AND p.is_public = 1 AND p.status = 1
        </if>
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询附近的帖子 -->
    <select id="selectNearbyPosts" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>,
            (6371 * acos(cos(radians(#{latitude})) * cos(radians(p.location_latitude)) * 
             cos(radians(p.location_longitude) - radians(#{longitude})) + 
             sin(radians(#{latitude})) * sin(radians(p.location_latitude)))) AS distance
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1
        AND p.location_latitude IS NOT NULL AND p.location_longitude IS NOT NULL
        HAVING distance &lt;= #{radius}
        ORDER BY distance ASC, p.create_time DESC
    </select>

    <!-- 查询热门帖子 -->
    <select id="selectHotPosts" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>,
            (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) as hot_score
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1
        <if test="days != null and days > 0">
            AND p.create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY hot_score DESC, p.create_time DESC
    </select>

    <!-- 查询关注用户的帖子 -->
    <select id="selectFollowingPosts" resultMap="PostVOResultMap">
        SELECT 
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        INNER JOIN user_follows uf ON p.user_id = uf.following_id AND uf.follower_id = #{currentUserId} AND uf.is_delete = 0
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1
        ORDER BY p.create_time DESC
    </select>

    <!-- 根据标签查询帖子 -->
    <select id="selectPostsByTags" resultMap="PostVOResultMap">
        SELECT DISTINCT
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        INNER JOIN post_tags pt ON p.id = pt.post_id
        INNER JOIN tags t ON pt.tag_id = t.id
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1
        AND t.name IN
        <foreach collection="tagNames" item="tagName" open="(" separator="," close=")">
            #{tagName}
        </foreach>
        ORDER BY p.create_time DESC
    </select>

    <!-- 批量查询帖子基本信息 -->
    <select id="selectBatchByIds" resultType="com.yupi.springbootinit.model.entity.Post">
        SELECT * FROM posts
        WHERE id IN
        <foreach collection="postIds" item="postId" open="(" separator="," close=")">
            #{postId}
        </foreach>
        AND is_delete = 0
    </select>

    <!-- 增加帖子浏览数 -->
    <update id="incrementViewCount">
        UPDATE posts SET view_count = view_count + 1 WHERE id = #{postId}
    </update>

    <!-- 统计用户的帖子数量 -->
    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM posts
        WHERE user_id = #{userId} AND is_delete = 0
    </select>

    <!-- 根据帖子ID列表分页查询帖子VO -->
    <select id="selectPostVOsByIds" resultMap="PostVOResultMap">
        SELECT
            <include refid="Base_Column_List"/>,
            <include refid="User_Interaction_Status"/>
        FROM posts p
        LEFT JOIN ba_user u ON p.user_id = u.id
        LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = #{currentUserId} AND pl.is_delete = 0
        LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = #{currentUserId} AND pf.is_delete = 0
        WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1
        <if test="postIds != null and postIds.size() > 0">
            AND p.id IN
            <foreach collection="postIds" item="postId" open="(" separator="," close=")">
                #{postId}
            </foreach>
        </if>
        <choose>
            <when test="sortBy == 'hot'">
                ORDER BY (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) DESC, p.create_time DESC
            </when>
            <when test="sortBy == 'comment'">
                ORDER BY p.comment_count DESC, p.create_time DESC
            </when>
            <otherwise>
                ORDER BY p.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
