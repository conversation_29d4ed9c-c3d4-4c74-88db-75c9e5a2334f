<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.NotificationMapper">

    <resultMap id="NotificationVOResultMap" type="com.yupi.springbootinit.model.vo.NotificationVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="senderId" column="sender_id" jdbcType="BIGINT"/>
        <result property="senderNickname" column="sender_nickname" jdbcType="VARCHAR"/>
        <result property="senderAvatar" column="sender_avatar" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="relatedId" column="related_id" jdbcType="BIGINT"/>
        <result property="relatedType" column="related_type" jdbcType="VARCHAR"/>
        <result property="isRead" column="is_read" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="readTime" column="read_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询用户通知列表 -->
    <select id="selectNotificationPage" resultMap="NotificationVOResultMap">
        SELECT 
            n.id, n.user_id, n.sender_id, n.type, n.title, n.content,
            n.related_id, n.related_type, n.is_read, n.create_time, n.read_time,
            u.nickname as sender_nickname, u.avatar as sender_avatar
        FROM notifications n
        LEFT JOIN ba_user u ON n.sender_id = u.id
        WHERE n.user_id = #{userId} AND n.is_delete = 0
        <if test="type != null">
            AND n.type = #{type}
        </if>
        <if test="isRead != null">
            AND n.is_read = #{isRead}
        </if>
        ORDER BY n.create_time DESC
    </select>

    <!-- 统计用户未读通知数 -->
    <select id="countUnreadNotifications" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM notifications 
        WHERE user_id = #{userId} AND is_read = 0 AND is_delete = 0
    </select>

    <!-- 按类型统计用户未读通知数 -->
    <select id="countUnreadNotificationsByType" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM notifications 
        WHERE user_id = #{userId} AND type = #{type} AND is_read = 0 AND is_delete = 0
    </select>

    <!-- 标记用户所有通知为已读 -->
    <update id="markAllAsRead">
        UPDATE notifications 
        SET is_read = 1, read_time = NOW()
        WHERE user_id = #{userId} AND is_read = 0 AND is_delete = 0
    </update>

    <!-- 标记指定类型的通知为已读 -->
    <update id="markTypeAsRead">
        UPDATE notifications 
        SET is_read = 1, read_time = NOW()
        WHERE user_id = #{userId} AND type = #{type} AND is_read = 0 AND is_delete = 0
    </update>

    <!-- 删除用户的所有通知 -->
    <update id="deleteAllByUserId">
        UPDATE notifications 
        SET is_delete = 1
        WHERE user_id = #{userId}
    </update>

    <!-- 删除过期通知 -->
    <delete id="deleteExpiredNotifications">
        DELETE FROM notifications 
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

</mapper>
