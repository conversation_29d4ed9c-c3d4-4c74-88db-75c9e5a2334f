<view class="user-profile-container data-v-9e24a6bc"><scroll-view class="content data-v-9e24a6bc" scroll-y="{{true}}"><block wx:if="{{loading}}"><view class="loading-state data-v-9e24a6bc"><u-loading vue-id="6cc868a4-1" mode="circle" size="24" class="data-v-9e24a6bc" bind:__l="__l"></u-loading><text class="loading-text data-v-9e24a6bc">加载中...</text></view></block><block wx:else><view class="user-section data-v-9e24a6bc"><view class="user-info data-v-9e24a6bc"><u-avatar vue-id="6cc868a4-2" src="{{userInfo.avatar}}" size="80" class="data-v-9e24a6bc" bind:__l="__l"></u-avatar><view class="user-details data-v-9e24a6bc"><text class="nickname data-v-9e24a6bc">{{userInfo.nickname||'用户'}}</text><text class="user-id data-v-9e24a6bc">{{"ID: "+userInfo.userId}}</text><text class="bio data-v-9e24a6bc">{{userInfo.bio||'这个人很懒，什么都没有留下...'}}</text></view><view class="action-buttons data-v-9e24a6bc"><view data-event-opts="{{[['tap',[['toggleFollow',['$event']]]]]}}" class="{{['custom-btn','follow-btn','data-v-9e24a6bc',(userInfo.isFollowed)?'followed':'']}}" bindtap="__e">{{userInfo.isFollowed?'已关注':'关注'}}</view><view data-event-opts="{{[['tap',[['goToChat',['$event']]]]]}}" class="custom-btn chat-btn data-v-9e24a6bc" bindtap="__e">私信</view></view></view><view class="stats-section data-v-9e24a6bc"><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.postCount}}</text><text class="stat-label data-v-9e24a6bc">帖子</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.followingCount}}</text><text class="stat-label data-v-9e24a6bc">关注</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.followersCount}}</text><text class="stat-label data-v-9e24a6bc">粉丝</text></view><view class="stat-item data-v-9e24a6bc"><text class="stat-number data-v-9e24a6bc">{{userInfo.likeCount}}</text><text class="stat-label data-v-9e24a6bc">获赞</text></view></view></view></block><view class="posts-section data-v-9e24a6bc"><view class="section-header data-v-9e24a6bc"><text class="section-title data-v-9e24a6bc">TA的帖子</text></view><view class="post-grid data-v-9e24a6bc"><block wx:for="{{userPosts}}" wx:for-item="post" wx:for-index="__i0__" wx:key="id"><post-card class="post-card-item data-v-9e24a6bc" vue-id="{{'6cc868a4-3-'+__i0__}}" post="{{post}}" data-event-opts="{{[['^click',[['goPostDetail']]],['^userClick',[['goUserProfile']]],['^like',[['onPostLike']]]]}}" bind:click="__e" bind:userClick="__e" bind:like="__e" bind:__l="__l"></post-card></block></view><block wx:if="{{!$root.g0}}"><view class="empty-state data-v-9e24a6bc"><u-icon vue-id="6cc868a4-4" name="file-text" color="#ccc" size="60" class="data-v-9e24a6bc" bind:__l="__l"></u-icon><text class="empty-text data-v-9e24a6bc">暂无帖子</text></view></block></view></scroll-view></view>