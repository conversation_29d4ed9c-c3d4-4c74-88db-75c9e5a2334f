2025-07-29 09:14:16.780 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 09:14:43.020 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 09:45:12.491 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 14:27:55.288 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 16:39:55.899 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 17:27:21.197 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.IllegalStateException: Unable to load cache item
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:79)
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:134)
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:319)
	at org.springframework.cglib.proxy.Enhancer.createHelper(Enhancer.java:572)
	at org.springframework.cglib.proxy.Enhancer.createClass(Enhancer.java:419)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:137)
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:109)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:447)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:268)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:325)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:147)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:746)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:564)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295)
	at com.yupi.springbootinit.MainApplication.main(MainApplication.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: java.lang.NoClassDefFoundError: com/yupi/springbootinit/websocket/ChatWebSocketHandler
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3373)
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2555)
	at org.springframework.cglib.proxy.Enhancer.generateClass(Enhancer.java:661)
	at org.springframework.cglib.transform.TransformingClassGenerator.generateClass(TransformingClassGenerator.java:33)
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:25)
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.generate(ClassLoaderAwareGeneratorStrategy.java:57)
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:358)
	at org.springframework.cglib.proxy.Enhancer.generate(Enhancer.java:585)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:110)
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData$3.apply(AbstractClassGenerator.java:108)
	at org.springframework.cglib.core.internal.LoadingCache$2.call(LoadingCache.java:54)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:61)
	... 25 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.yupi.springbootinit.websocket.ChatWebSocketHandler
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:145)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 39 common frames omitted
2025-07-29 17:27:26.436 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-29 17:39:52.701 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
