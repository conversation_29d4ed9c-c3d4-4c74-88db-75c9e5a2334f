<view class="message-detail-container data-v-01825956"><scroll-view class="content-scroll data-v-01825956" scroll-y="{{true}}"><view class="message-detail data-v-01825956"><view class="message-header data-v-01825956"><view class="{{['message-icon','data-v-01825956','icon-'+messageDetail.type]}}"><u-icon vue-id="2ec95270-1" name="{{$root.m0}}" size="24" color="#fff" class="data-v-01825956" bind:__l="__l"></u-icon></view><view class="header-info data-v-01825956"><text class="message-title data-v-01825956">{{messageDetail.title}}</text><text class="message-time data-v-01825956">{{$root.m1}}</text></view></view><view class="message-body data-v-01825956"><rich-text nodes="{{messageDetail.htmlContent||messageDetail.content}}" class="data-v-01825956"></rich-text></view><block wx:if="{{$root.g0}}"><view class="action-buttons data-v-01825956"><block wx:for="{{messageDetail.actions}}" wx:for-item="action" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['handleAction',['$0'],[[['messageDetail.actions','id',action.id]]]]]]]}}" class="{{['action-btn','data-v-01825956',action.type]}}" bindtap="__e"><text class="btn-text data-v-01825956">{{action.text}}</text></view></block></view></block><block wx:if="{{$root.g1}}"><view class="attachments data-v-01825956"><text class="section-title data-v-01825956">相关附件</text><block wx:for="{{messageDetail.attachments}}" wx:for-item="attachment" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['openAttachment',['$0'],[[['messageDetail.attachments','id',attachment.id]]]]]]]}}" class="attachment-item data-v-01825956" bindtap="__e"><u-icon vue-id="{{'2ec95270-2-'+__i1__}}" name="file-text" size="20" color="#2979ff" class="data-v-01825956" bind:__l="__l"></u-icon><text class="attachment-name data-v-01825956">{{attachment.name}}</text><u-icon vue-id="{{'2ec95270-3-'+__i1__}}" name="arrow-right" size="16" color="#999" class="data-v-01825956" bind:__l="__l"></u-icon></view></block></view></block></view></scroll-view></view>