@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.container.data-v-19a3a4c2 {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-19a3a4c2 {
  text-align: center;
  margin-bottom: 30rpx;
}
.title.data-v-19a3a4c2 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-19a3a4c2 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.info-item.data-v-19a3a4c2 {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}
.label.data-v-19a3a4c2 {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}
.value.data-v-19a3a4c2 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}
.status-connected.data-v-19a3a4c2 {
  color: #4caf50;
}
.status-connecting.data-v-19a3a4c2 {
  color: #ff9800;
}
.status-disconnected.data-v-19a3a4c2 {
  color: #f44336;
}
.button-section.data-v-19a3a4c2 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.test-btn.data-v-19a3a4c2,
.disconnect-btn.data-v-19a3a4c2,
.send-btn.data-v-19a3a4c2 {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
}
.test-btn.data-v-19a3a4c2 {
  background: #2979ff;
  color: white;
}
.disconnect-btn.data-v-19a3a4c2 {
  background: #f44336;
  color: white;
}
.send-btn.data-v-19a3a4c2 {
  background: #4caf50;
  color: white;
}
.log-section.data-v-19a3a4c2 {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  height: 600rpx;
}
.log-title.data-v-19a3a4c2 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.log-content.data-v-19a3a4c2 {
  height: 500rpx;
}
.log-item.data-v-19a3a4c2 {
  display: flex;
  margin-bottom: 15rpx;
  padding: 10rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}
.log-time.data-v-19a3a4c2 {
  font-size: 24rpx;
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
}
.log-message.data-v-19a3a4c2 {
  font-size: 26rpx;
  flex: 1;
}
.log-message.success.data-v-19a3a4c2 {
  color: #4caf50;
}
.log-message.error.data-v-19a3a4c2 {
  color: #f44336;
}
.log-message.warning.data-v-19a3a4c2 {
  color: #ff9800;
}
.log-message.info.data-v-19a3a4c2 {
  color: #333;
}

