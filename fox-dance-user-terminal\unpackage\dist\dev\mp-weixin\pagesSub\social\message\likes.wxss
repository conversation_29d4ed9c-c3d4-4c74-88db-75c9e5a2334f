@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.likes-container.data-v-541b87c7 {
  background: #f5f5f5;
}
.message-list.data-v-541b87c7 {
  height: 100%;
  padding: 32rpx 0;
}
.message-card.data-v-541b87c7 {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  margin: 0 32rpx 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.user-avatar.data-v-541b87c7 {
  margin-right: 24rpx;
  flex-shrink: 0;
}
.message-content.data-v-541b87c7 {
  flex: 1;
}
.message-header.data-v-541b87c7 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.user-name.data-v-541b87c7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 8rpx;
}
.action-type.data-v-541b87c7 {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
}
.post-type.data-v-541b87c7 {
  font-size: 28rpx;
  color: #2979ff;
  margin-right: 16rpx;
}
.message-time.data-v-541b87c7 {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
}
.comment-content.data-v-541b87c7 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: block;
}
.related-post.data-v-541b87c7 {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e4e7ed;
}
.post-cover.data-v-541b87c7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.post-title.data-v-541b87c7 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}
.unread-dot.data-v-541b87c7 {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
}
.empty-state.data-v-541b87c7 {
  display: flex;
  justify-content: center;
  padding: 120rpx 32rpx;
}
.load-more.data-v-541b87c7 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}
.load-text.data-v-541b87c7 {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

