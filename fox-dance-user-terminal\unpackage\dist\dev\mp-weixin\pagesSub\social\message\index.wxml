<view class="message-container data-v-2caef1dd"><view class="header data-v-2caef1dd"><view class="header-content data-v-2caef1dd"><text class="title data-v-2caef1dd">消息</text><view class="header-actions data-v-2caef1dd"><u-icon vue-id="692fcac9-1" name="search" size="24" color="#333" data-event-opts="{{[['^click',[['goSearch']]]]}}" bind:click="__e" class="data-v-2caef1dd" bind:__l="__l"></u-icon><u-icon vue-id="692fcac9-2" name="plus" size="24" color="#333" data-event-opts="{{[['^click',[['startNewChat']]]]}}" bind:click="__e" class="data-v-2caef1dd" bind:__l="__l"></u-icon></view></view></view><view class="page-content data-v-2caef1dd"><view class="quick-actions data-v-2caef1dd"><view data-event-opts="{{[['tap',[['goSystemMessages',['$event']]]]]}}" class="action-item data-v-2caef1dd" bindtap="__e"><view class="action-icon system data-v-2caef1dd"><u-icon vue-id="692fcac9-3" name="bell" color="#fff" size="20" class="data-v-2caef1dd" bind:__l="__l"></u-icon></view><text class="action-text data-v-2caef1dd">系统消息</text><block wx:if="{{systemUnreadCount}}"><view class="unread-badge data-v-2caef1dd">{{systemUnreadCount}}</view></block></view><view data-event-opts="{{[['tap',[['goLikeMessages',['$event']]]]]}}" class="action-item data-v-2caef1dd" bindtap="__e"><view class="action-icon like data-v-2caef1dd"><u-icon vue-id="692fcac9-4" name="heart" color="#fff" size="20" class="data-v-2caef1dd" bind:__l="__l"></u-icon></view><text class="action-text data-v-2caef1dd">赞和评论</text><block wx:if="{{likeUnreadCount}}"><view class="unread-badge data-v-2caef1dd">{{likeUnreadCount}}</view></block></view><view data-event-opts="{{[['tap',[['goFollowMessages',['$event']]]]]}}" class="action-item data-v-2caef1dd" bindtap="__e"><view class="action-icon follow data-v-2caef1dd"><u-icon vue-id="692fcac9-5" name="account-fill" color="#fff" size="20" class="data-v-2caef1dd" bind:__l="__l"></u-icon></view><text class="action-text data-v-2caef1dd">新粉丝</text><block wx:if="{{followUnreadCount}}"><view class="unread-badge data-v-2caef1dd">{{followUnreadCount}}</view></block></view></view><view class="chat-list data-v-2caef1dd"><block wx:for="{{$root.l0}}" wx:for-item="chat" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['openChat',['$0'],[[['chatList','id',chat.$orig.id]]]]]],['longpress',[['showChatActions',['$0'],[[['chatList','id',chat.$orig.id]]]]]]]}}" class="chat-item data-v-2caef1dd" bindtap="__e" bindlongpress="__e"><view class="chat-avatar data-v-2caef1dd"><u-avatar vue-id="{{'692fcac9-6-'+__i0__}}" src="{{chat.$orig.avatar}}" size="50" class="data-v-2caef1dd" bind:__l="__l"></u-avatar><block wx:if="{{chat.$orig.isOnline}}"><view class="online-dot data-v-2caef1dd"></view></block></view><view class="chat-content data-v-2caef1dd"><view class="chat-header data-v-2caef1dd"><text class="chat-name data-v-2caef1dd">{{chat.$orig.name}}</text><text class="chat-time data-v-2caef1dd">{{chat.m0}}</text></view><view class="chat-preview data-v-2caef1dd"><view class="message-preview data-v-2caef1dd"><block wx:if="{{chat.$orig.lastMessageType==='text'}}"><text class="preview-text data-v-2caef1dd">{{chat.$orig.lastMessage}}</text></block><block wx:else><block wx:if="{{chat.$orig.lastMessageType==='image'}}"><text class="preview-text data-v-2caef1dd">[图片]</text></block><block wx:else><block wx:if="{{chat.$orig.lastMessageType==='voice'}}"><text class="preview-text data-v-2caef1dd">[语音]</text></block><block wx:else><text class="preview-text data-v-2caef1dd">{{chat.$orig.lastMessage}}</text></block></block></block></view><view class="chat-status data-v-2caef1dd"><block wx:if="{{chat.$orig.unreadCount}}"><view class="unread-count data-v-2caef1dd">{{chat.$orig.unreadCount>99?'99+':chat.$orig.unreadCount}}</view></block></view></view></view></view></block><block wx:if="{{$root.g0}}"><view class="empty-state data-v-2caef1dd"><u-icon vue-id="692fcac9-7" name="chat" color="#ccc" size="60" class="data-v-2caef1dd" bind:__l="__l"></u-icon><text class="empty-text data-v-2caef1dd">暂无消息</text><text class="empty-desc data-v-2caef1dd">开始与朋友聊天吧</text></view></block><block wx:if="{{loading}}"><view class="loading-state data-v-2caef1dd"><u-loading vue-id="692fcac9-8" mode="circle" size="24" class="data-v-2caef1dd" bind:__l="__l"></u-loading><text class="loading-text data-v-2caef1dd">加载中...</text></view></block></view></view></view>