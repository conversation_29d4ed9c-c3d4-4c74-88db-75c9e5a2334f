@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.u-tag.data-v-23f5e68e {
  box-sizing: border-box;
  align-items: center;
  border-radius: 6rpx;
  display: inline-block;
  line-height: 1;
}
.u-size-default.data-v-23f5e68e {
  font-size: 22rpx;
  padding: 12rpx 22rpx;
}
.u-size-mini.data-v-23f5e68e {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
}
.u-mode-light-primary.data-v-23f5e68e {
  background-color: #ecf5ff;
  color: #ff2c3c;
  border: 1px solid #a0cfff;
}
.u-mode-light-success.data-v-23f5e68e {
  background-color: #dbf1e1;
  color: #19be6b;
  border: 1px solid #71d5a1;
}
.u-mode-light-error.data-v-23f5e68e {
  background-color: #fef0f0;
  color: #fa3534;
  border: 1px solid #fab6b6;
}
.u-mode-light-warning.data-v-23f5e68e {
  background-color: #fdf6ec;
  color: #ff9900;
  border: 1px solid #fcbd71;
}
.u-mode-light-info.data-v-23f5e68e {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #c8c9cc;
}
.u-mode-dark-primary.data-v-23f5e68e {
  background-color: #ff2c3c;
  color: #FFFFFF;
}
.u-mode-dark-success.data-v-23f5e68e {
  background-color: #19be6b;
  color: #FFFFFF;
}
.u-mode-dark-error.data-v-23f5e68e {
  background-color: #fa3534;
  color: #FFFFFF;
}
.u-mode-dark-warning.data-v-23f5e68e {
  background-color: #ff9900;
  color: #FFFFFF;
}
.u-mode-dark-info.data-v-23f5e68e {
  background-color: #909399;
  color: #FFFFFF;
}
.u-mode-plain-primary.data-v-23f5e68e {
  background-color: #FFFFFF;
  color: #ff2c3c;
  border: 1px solid #ff2c3c;
}
.u-mode-plain-success.data-v-23f5e68e {
  background-color: #FFFFFF;
  color: #19be6b;
  border: 1px solid #19be6b;
}
.u-mode-plain-error.data-v-23f5e68e {
  background-color: #FFFFFF;
  color: #fa3534;
  border: 1px solid #fa3534;
}
.u-mode-plain-warning.data-v-23f5e68e {
  background-color: #FFFFFF;
  color: #ff9900;
  border: 1px solid #ff9900;
}
.u-mode-plain-info.data-v-23f5e68e {
  background-color: #FFFFFF;
  color: #909399;
  border: 1px solid #909399;
}
.u-disabled.data-v-23f5e68e {
  opacity: 0.55;
}
.u-shape-circle.data-v-23f5e68e {
  border-radius: 100rpx;
}
.u-shape-circleRight.data-v-23f5e68e {
  border-radius: 0 100rpx 100rpx 0;
}
.u-shape-circleLeft.data-v-23f5e68e {
  border-radius: 100rpx 0 0 100rpx;
}
.u-close-icon.data-v-23f5e68e {
  margin-left: 14rpx;
  font-size: 22rpx;
  color: #19be6b;
}
.u-icon-wrap.data-v-23f5e68e {
  display: inline-flex;
  -webkit-transform: scale(0.86);
          transform: scale(0.86);
}

