{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/App.vue?b3c5", "uni-app:///App.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/App.vue?5f50", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/App.vue?1851"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "component", "MescrollBody", "use", "uView", "prototype", "$toast", "toast", "$baseUrl", "$baseUrl_admin", "$baseUrlOss", "$baseUrl_ht", "mixin", "share", "config", "productionTip", "App", "mpType", "app", "$mount", "globalData", "selectedImages", "onLaunch", "uni", "console", "onShow", "onHide", "methods"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAS3D;AACA;AACA;AAKA;AAEA;AAEA;AA8CA;AAAoC;AAAA;AAnEpC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAAC;EAAA;IAAA;EAAA;AAAA;AAc3DC,YAAG,CAACC,SAAS,CAAC,eAAe,EAAEC,YAAY,CAAC;AAC5C;;AAEAF,YAAG,CAACG,GAAG,CAACC,gBAAK,CAAC;AAMdJ,YAAG,CAACK,SAAS,CAACC,MAAM,GAAGC,YAAK;AAC5B;AACAP,YAAG,CAACK,SAAS,CAACG,QAAQ,GAAG,8BAA8B;AACvDR,YAAG,CAACK,SAAS,CAACI,cAAc,GAAG,+BAA+B;AAC9D;AACAT,YAAG,CAACK,SAAS,CAACK,WAAW,GAAG,8BAA8B;AAC1DV,YAAG,CAACK,SAAS,CAACM,WAAW,GAAG,kCAAkC;;AAI9D;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAEAX,YAAG,CAACY,KAAK,CAACC,cAAK,CAAC;AAEhBb,YAAG,CAACc,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIlB,YAAG,mBACfgB,YAAG,EACL;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AC5EZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACsL;AACtL,gBAAgB,6LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA+rB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACEntB;;eACA;EACAC;IACAC;EACA;;EACAC;IACAC;IACAC;IACAD;IACAA;EACA;EACAE;IACAD;EACA;EACAE;IACAF;EACA;EACAG;AACA;AAAA,2B;;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAAszC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA10C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n// 引入浏览器兼容性修复\r\n\r\n\r\n\r\n\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor.js'\r\nimport httpApi from '@/config/http.api.js'\r\n//引入mescrollBody 组件\r\nimport MescrollBody from \"@/components/mescroll-uni/mescroll-body.vue\"\r\nVue.component('mescroll-body', MescrollBody)\r\n//引入 uview-ui\r\nimport uView from \"@/components/uview-ui\";\r\nVue.use(uView);\r\nimport utils from '@/utils/utils.js'\r\n// 轻提示\r\nimport {\r\n\ttoast\r\n} from './utils/tools'\r\nVue.prototype.$toast = toast\r\n// Vue.prototype.$baseUrl = 'https://danceadmin.xinzhiyukeji.cn'\r\nVue.prototype.$baseUrl = 'https://file.foxdance.com.cn'\r\nVue.prototype.$baseUrl_admin = 'https://admin.foxdance.com.cn'\r\n// Vue.prototype.$baseUrlOss = 'https://dance-1329875799.cos.ap-guangzhou.myqcloud.com'\r\nVue.prototype.$baseUrlOss = 'https://file.foxdance.com.cn'\r\nVue.prototype.$baseUrl_ht = 'https://contract.foxdance.com.cn'\r\n\r\n\r\n\r\n// import ZAudio from '@/components/uniapp-zaudio' //HbuilderX插件导入方式, import可能需要修改目录名哦\r\n// // import ZAudio from 'uniapp-zaudio' // npm引用方式\r\n\r\n// let zaudio = new ZAudio({\r\n//   // continuePlay: true, //续播\r\n//   autoPlay: false, //自动播放 部分浏览器不支持\r\n// });\r\n// Vue.prototype.$zaudio = zaudio; //挂载vue原型链上\r\n\r\n// //模拟音频初始数据,切勿业务中使用\r\n// var data = [\r\n//   {\r\n//     src:\r\n//       \"https://96.f.1ting.com/local_to_cube_202004121813/96kmp3/zzzzzmp3/2016aJan/18X/18d_DeH/01.mp3\",\r\n//     title: \"恭喜发财\",\r\n//     singer: \"刘德华\",\r\n//     coverImgUrl:\r\n//       \"https://img.1ting.com/images/special/75/s150_f84ef5082b0420f74cd2546b986ab0fc.jpg\",\r\n//   },\r\n//   {\r\n//     src:\r\n//       \"https://96.f.1ting.com/local_to_cube_202004121813/96kmp3/zzzzzmp3/2015kNov/25X/25m_XiaoQ/03.mp3\",\r\n//     title: \"好运来\",\r\n//     singer: \"作者1111\",\r\n//     coverImgUrl:\r\n//       \"https://img.1ting.com/images/special/204/s150_77254cd4a4da1a33b8faf89c4cbf6e40.jpg\",\r\n//   },\r\n// ];\r\n// zaudio.setAudio(data); //添加音频\r\n// zaudio.setRender(0)//渲染第一首音频\r\n\r\n\r\n// 分享 \r\nimport share from '@/utils/share.js'\t\r\nVue.mixin(share)\r\n\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "\r\n<script>\r\nimport { config } from \"@/config/http.achieve.js\";\r\nexport default {\r\n  globalData: {\r\n    selectedImages: null // 用于存储从TabBar选择的图片\r\n  },\r\n  onLaunch: function() {\r\n    uni.hideTabBar();\r\n    console.log(\"App Launch\");\r\n    uni.removeStorageSync(\"pageIndex\");\r\n    uni.removeStorageSync(\"pageZiliao\");\r\n  },\r\n  onShow: function() {\r\n    console.log(\"App Show\");\r\n  },\r\n  onHide: function() {\r\n    console.log(\"App Hide\");\r\n  },\r\n  methods: {}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"@/components/uview-ui/index.scss\";\r\n@import \"@/styles/base.scss\";\r\n@import \"@/styles/common.scss\";\r\n@import \"@/styles/style.css\";\r\n@import \"@/styles/style_fz.css\";\r\n@import \"@/styles/animate.min.css\";\r\n\r\n/* 更具体的选择器，针对发布按钮（第三个项） */\r\n.u-tabbar .u-tabbar__content .u-tabbar__content__item:nth-child(3) .u-icon .u-icon__img {\r\n  width: 70rpx !important;\r\n  height: 60rpx !important;\r\n  margin-top: 5rpx;\r\n}\r\n\r\n\r\npage {\r\n  background-color: #f6f6f6;\r\n}\r\nimage {\r\n  display: block;\r\n}\r\n\r\n/*每个页面公共css */\r\n// 使用时仅需设置 宽高 圆角 和字号\r\n.btn {\r\n  background-color: #131315;\r\n  font-weight: 500;\r\n  color: #ffffff;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  z-index: 11;\r\n}\r\n\r\n.inputRow {\r\n  height: 110rpx;\r\n  box-sizing: border-box;\r\n  padding: 0 40rpx;\r\n  background-color: #fff;\r\n  border-bottom: 2rpx solid #f8f8f8;\r\n\r\n  .laber {\r\n    line-height: 110rpx;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333333;\r\n\r\n    image {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin-right: 14rpx;\r\n      margin-left: 0;\r\n    }\r\n  }\r\n\r\n  input {\r\n    flex: 1;\r\n    height: 100%;\r\n    text-align: right;\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    line-height: 110rpx;\r\n  }\r\n\r\n  image {\r\n    width: 16rpx;\r\n    height: 28rpx;\r\n    margin-left: 24rpx;\r\n    margin-top: 4rpx;\r\n  }\r\n\r\n  text {\r\n    margin-left: 24rpx;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333333;\r\n  }\r\n\r\n  .switch {\r\n    width: 113rpx;\r\n    height: 58rpx;\r\n  }\r\n\r\n  textarea {\r\n    width: 100%;\r\n    height: 130rpx;\r\n    font-size: 30rpx;\r\n  }\r\n}\r\n\r\n//晨阳设计图 二次确认弹窗常用样式  参考 /pages/setting/setting 退出登录弹窗\r\n.prompt {\r\n  width: 600rpx;\r\n  height: 340rpx;\r\n  background: #ffffff;\r\n  box-shadow: 0 0 10rpx 0 rgba(228, 239, 244, 0.6);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  .prompt_t {\r\n    box-sizing: border-box;\r\n    padding: 22rpx 0 0 25rpx;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    image {\r\n      display: block;\r\n      width: 33rpx;\r\n      height: 33rpx;\r\n      margin-right: 24rpx;\r\n    }\r\n\r\n    .prompt_t_text {\r\n      font-size: 26rpx;\r\n      font-weight: 400;\r\n      color: #e93b3d;\r\n      line-height: 36rpx;\r\n    }\r\n  }\r\n\r\n  .prompt_c {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-sizing: border-box;\r\n    padding: 0 40rpx;\r\n    text-align: center;\r\n  }\r\n\r\n  .prompt_d {\r\n    height: 113rpx;\r\n    display: flex;\r\n    border-top: 1rpx solid #d5d5d5;\r\n\r\n    .prompt_d_l {\r\n      width: 50%;\r\n      border-right: 1rpx solid #d5d5d5;\r\n      text-align: center;\r\n      font-size: 32rpx;\r\n      font-weight: 500;\r\n      color: #8d8d8d;\r\n      line-height: 113rpx;\r\n    }\r\n\r\n    .prompt_d_r {\r\n      text-align: center;\r\n      width: 50%;\r\n      font-size: 32rpx;\r\n      font-weight: 500;\r\n      color: #000;\r\n      line-height: 113rpx;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753838383606\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}