<view class="container data-v-19a3a4c2"><view class="header data-v-19a3a4c2"><text class="title data-v-19a3a4c2">WebSocket连接测试</text></view><view class="test-section data-v-19a3a4c2"><view class="info-item data-v-19a3a4c2"><text class="label data-v-19a3a4c2">当前环境：</text><text class="value data-v-19a3a4c2">{{envInfo.platform+" - "+envInfo.env}}</text></view><view class="info-item data-v-19a3a4c2"><text class="label data-v-19a3a4c2">WebSocket URL：</text><text class="value data-v-19a3a4c2">{{wsUrl}}</text></view><view class="info-item data-v-19a3a4c2"><text class="label data-v-19a3a4c2">连接状态：</text><text class="{{['value','data-v-19a3a4c2',statusClass]}}">{{connectionStatus}}</text></view></view><view class="button-section data-v-19a3a4c2"><button class="test-btn data-v-19a3a4c2" disabled="{{isConnecting}}" data-event-opts="{{[['tap',[['testConnection',['$event']]]]]}}" bindtap="__e">{{isConnecting?'连接中...':'测试连接'}}</button><button class="disconnect-btn data-v-19a3a4c2" disabled="{{!isConnected}}" data-event-opts="{{[['tap',[['disconnect',['$event']]]]]}}" bindtap="__e">断开连接</button><button class="send-btn data-v-19a3a4c2" disabled="{{!isConnected}}" data-event-opts="{{[['tap',[['sendTestMessage',['$event']]]]]}}" bindtap="__e">发送测试消息</button></view><view class="log-section data-v-19a3a4c2"><text class="log-title data-v-19a3a4c2">连接日志：</text><scroll-view class="log-content data-v-19a3a4c2" scroll-y="{{true}}"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><view class="log-item data-v-19a3a4c2"><text class="log-time data-v-19a3a4c2">{{log.time}}</text><text class="{{['log-message','data-v-19a3a4c2',log.type]}}">{{log.message}}</text></view></block></scroll-view></view></view>