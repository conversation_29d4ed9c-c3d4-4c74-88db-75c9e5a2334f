@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.search-container.data-v-2813d7e8 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}
.search-bar.data-v-2813d7e8 {
  padding: 16rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
}
.discovery-section.data-v-2813d7e8 {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;
}
.search-results.data-v-2813d7e8 {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 0;
  /* 配合flex: 1使用，确保高度计算正确 */
  box-sizing: border-box;
}
.history-section.data-v-2813d7e8,
.hot-searches-section.data-v-2813d7e8 {
  margin-bottom: 40rpx;
}
.section-header.data-v-2813d7e8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title.data-v-2813d7e8 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}
.tags-container.data-v-2813d7e8 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.results-list.data-v-2813d7e8 {
  width: 100%;
  box-sizing: border-box;
}
.post-card-item.data-v-2813d7e8,
.user-card-item.data-v-2813d7e8,
.tag-card-item.data-v-2813d7e8 {
  width: 100%;
  margin-bottom: 24rpx;
  box-sizing: border-box;
  flex-shrink: 0;
  /* 防止被压缩 */
}
.post-card-item.data-v-2813d7e8:last-child,
.user-card-item.data-v-2813d7e8:last-child,
.tag-card-item.data-v-2813d7e8:last-child {
  margin-bottom: 0;
}
/* 调试信息样式 */
.debug-info.data-v-2813d7e8 {
  padding: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  color: #666;
}
/* 搜索标签页 */
.search-tabs.data-v-2813d7e8 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #fff;
  border-bottom: 1rpx solid #e4e7ed;
  padding: 24rpx 32rpx;
  gap: 24rpx;
}
/* 自定义标签样式 */
.search-tabs.data-v-2813d7e8 .u-tag {
  min-width: 120rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.search-tabs.data-v-2813d7e8 .u-tag:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
/* 搜索结果内容 */
.results-content.data-v-2813d7e8 {
  flex: 1;
  width: 100%;
  height: 100%;
  padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
}
/* 加载状态 */
.loading-container.data-v-2813d7e8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.loading-text.data-v-2813d7e8 {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #909399;
}
/* 加载更多 */
.load-more.data-v-2813d7e8 {
  padding: 40rpx 0;
  text-align: center;
}
.loading-more.data-v-2813d7e8 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}
.load-more-tip.data-v-2813d7e8 {
  font-size: 28rpx;
  color: #909399;
}
.no-more.data-v-2813d7e8 {
  padding: 40rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #c0c4cc;
}
/* 空状态 */
.empty-container.data-v-2813d7e8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}
.empty-tips.data-v-2813d7e8 {
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #909399;
}
.back-link.data-v-2813d7e8 {
  color: #ff6b87;
  text-decoration: underline;
  margin-left: 8rpx;
}
/* 全部搜索结果分组样式 */
.result-section.data-v-2813d7e8 {
  margin-bottom: 40rpx;
}
.section-title.data-v-2813d7e8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24rpx;
  padding-left: 8rpx;
  border-left: 6rpx solid #ff6b87;
}
.view-more.data-v-2813d7e8 {
  margin-top: 24rpx;
  padding: 20rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #ff6b87;
  border: 2rpx solid #ff6b87;
  transition: all 0.3s ease;
}
.view-more.data-v-2813d7e8:active {
  background-color: #ff6b87;
  color: #ffffff;
}
/* 卡片间距调整 */
.user-card-item.data-v-2813d7e8,
.tag-card-item.data-v-2813d7e8,
.post-card-item.data-v-2813d7e8 {
  margin-bottom: 24rpx;
}
.user-card-item.data-v-2813d7e8:last-child,
.tag-card-item.data-v-2813d7e8:last-child,
.post-card-item.data-v-2813d7e8:last-child {
  margin-bottom: 0;
}

