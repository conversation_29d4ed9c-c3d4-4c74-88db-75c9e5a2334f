@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.chat-container.data-v-7a033bbb {
  height: 100vh;
  background: #f0f0f0;
  position: relative;
}
.header.data-v-7a033bbb {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
}
.header-content.data-v-7a033bbb {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.header-left.data-v-7a033bbb {
  display: flex;
  align-items: center;
  flex: 1;
}
.avatar-container.data-v-7a033bbb {
  position: relative;
  margin-right: 24rpx;
}
.header-avatar.data-v-7a033bbb {
  cursor: pointer;
}
.chat-info.data-v-7a033bbb {
  cursor: pointer;
  flex: 1;
}
.name-container.data-v-7a033bbb {
  position: relative;
  display: inline-block;
}
.chat-name.data-v-7a033bbb {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  padding-right: 20rpx;
}
.online-indicator.data-v-7a033bbb {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  -webkit-animation: online-pulse-data-v-7a033bbb 2s infinite;
          animation: online-pulse-data-v-7a033bbb 2s infinite;
}
@-webkit-keyframes online-pulse-data-v-7a033bbb {
0% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
}
50% {
    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);
}
100% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
}
}
@keyframes online-pulse-data-v-7a033bbb {
0% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
}
50% {
    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);
}
100% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
}
}
.header-actions.data-v-7a033bbb {
  display: flex;
  gap: 32rpx;
}
.message-list.data-v-7a033bbb {
  position: fixed;
  top: calc(88rpx + 25px + 2rpx);
  bottom: 120rpx;
  left: 0;
  right: 0;
  padding: 32rpx;
  overflow-y: auto;
  width: auto;
}
.message-item.data-v-7a033bbb {
  margin-bottom: 32rpx;
  margin-top: 32rpx;
}
.time-divider.data-v-7a033bbb {
  text-align: center;
  margin-bottom: 32rpx;
}
.time-text.data-v-7a033bbb {
  font-size: 24rpx;
  color: #999;
  background: rgba(0, 0, 0, 0.1);
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
}
.message-wrapper.data-v-7a033bbb {
  display: flex;
  align-items: flex-end;
}
.message-wrapper.is-mine.data-v-7a033bbb {
  justify-content: flex-end;
}
.message-avatar.data-v-7a033bbb {
  margin: 0 16rpx;
  cursor: pointer;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.message-avatar.data-v-7a033bbb:hover {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.message-content.data-v-7a033bbb {
  display: flex;
  flex-direction: column;
  width: auto;
}
/* 对方消息：内容左对齐 */
.message-wrapper:not(.is-mine) .message-content.data-v-7a033bbb {
  align-items: flex-start;
}
/* 我的消息：内容右对齐 */
.message-wrapper.is-mine .message-content.data-v-7a033bbb {
  align-items: flex-end;
}
.message-bubble.data-v-7a033bbb {
  padding: 24rpx 32rpx;
  border-radius: 36rpx;
  margin-bottom: 8rpx;
}
.text-bubble.data-v-7a033bbb {
  background: #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.text-bubble.mine.data-v-7a033bbb {
  background: #2979ff;
}
.message-text.data-v-7a033bbb {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}
.mine .message-text.data-v-7a033bbb {
  color: #fff;
}
.image-bubble.data-v-7a033bbb {
  padding: 0;
  overflow: hidden;
  background: transparent;
}
.message-image.data-v-7a033bbb {
  width: 300rpx;
  height: 300rpx;
  border-radius: 24rpx;
}
.voice-bubble.data-v-7a033bbb {
  background: #2979ff;
  display: flex;
  align-items: center;
  min-width: 160rpx;
  position: relative;
}
.voice-duration.data-v-7a033bbb {
  color: #fff;
  font-size: 28rpx;
  margin-left: 16rpx;
}
.voice-animation.data-v-7a033bbb {
  display: flex;
  gap: 4rpx;
  margin-left: 16rpx;
}
.wave.data-v-7a033bbb {
  width: 4rpx;
  height: 24rpx;
  background: #fff;
  -webkit-animation: wave-data-v-7a033bbb 1s infinite;
          animation: wave-data-v-7a033bbb 1s infinite;
}
.wave.data-v-7a033bbb:nth-child(2) {
  -webkit-animation-delay: 0.1s;
          animation-delay: 0.1s;
}
.wave.data-v-7a033bbb:nth-child(3) {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}
@-webkit-keyframes wave-data-v-7a033bbb {
0%,
  100% {
    height: 8rpx;
}
50% {
    height: 24rpx;
}
}
@keyframes wave-data-v-7a033bbb {
0%,
  100% {
    height: 8rpx;
}
50% {
    height: 24rpx;
}
}
.message-status.data-v-7a033bbb {
  margin-top: 8rpx;
}
.input-area.data-v-7a033bbb {
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
}
.extensions-panel.data-v-7a033bbb,
.emoji-panel.data-v-7a033bbb {
  height: 400rpx;
  border-bottom: 2rpx solid #e4e7ed;
}
.extension-grid.data-v-7a033bbb {
  display: flex;
  flex-wrap: wrap;
  padding: 40rpx;
  gap: 40rpx;
}
.extension-item.data-v-7a033bbb {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 30rpx);
}
.extension-icon.data-v-7a033bbb {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.extension-icon.photo.data-v-7a033bbb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.extension-icon.voice.data-v-7a033bbb {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.extension-icon.location.data-v-7a033bbb {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.extension-icon.file.data-v-7a033bbb {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.extension-text.data-v-7a033bbb {
  font-size: 24rpx;
  color: #666;
}
.emoji-scroll.data-v-7a033bbb {
  height: 100%;
  padding: 32rpx;
}
.emoji-grid.data-v-7a033bbb {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.emoji-item.data-v-7a033bbb {
  font-size: 48rpx;
  padding: 16rpx;
  text-align: center;
  width: 80rpx;
  height: 80rpx;
  line-height: 48rpx;
}
.input-bar.data-v-7a033bbb {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: flex-end;
  padding: 16rpx 24rpx;
  gap: 16rpx;
  min-height: 104rpx;
  background: #fff;
  border-top: 1rpx solid #e4e7ed;
  /* 确保在键盘弹起时的兼容性 */
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  transition: all 0.3s ease;
}
.input-bar.input-focused.data-v-7a033bbb {
  border-top-color: rgba(41, 121, 255, 0.2);
  background: #fafbfc;
}
.voice-btn.data-v-7a033bbb {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 36rpx;
  background: #f5f5f5;
  transition: all 0.2s ease;
}
.voice-btn.data-v-7a033bbb:active {
  background: #e8e8e8;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.input-wrapper.data-v-7a033bbb {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  min-height: 72rpx;
  max-height: 240rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}
.input-wrapper.has-text.data-v-7a033bbb {
  background: #f0f0f0;
}
.input-wrapper.data-v-7a033bbb:focus-within {
  background: #f0f0f0;
  border-color: rgba(41, 121, 255, 0.3);
  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.1);
}
/* 原生textarea样式 */
.input-textarea.data-v-7a033bbb {
  width: 100%;
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  min-height: 40rpx;
  max-height: 200rpx;
  padding: 0;
  box-sizing: border-box;
  word-break: break-all;
  white-space: pre-wrap;
}
.input-textarea.data-v-7a033bbb::-webkit-input-placeholder {
  color: #999;
  font-size: 32rpx;
}
.input-textarea.data-v-7a033bbb::placeholder {
  color: #999;
  font-size: 32rpx;
}
/* 字数统计 */
.char-count.data-v-7a033bbb {
  position: absolute;
  bottom: 8rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
}
.char-count .over-limit.data-v-7a033bbb {
  color: #ff4757;
  font-weight: 500;
}
/* 防止输入时的布局跳动 */
.input-wrapper.data-v-7a033bbb {
  will-change: height;
  contain: layout style;
}
.voice-record-btn.data-v-7a033bbb {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}
.voice-record-btn.record-ready.data-v-7a033bbb:active {
  background: #e8e8e8;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.voice-record-btn.recording.data-v-7a033bbb {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  border-color: #ff4757;
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}
.record-icon.data-v-7a033bbb {
  margin-right: 8rpx;
}
.record-text.data-v-7a033bbb {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}
.recording .record-text.data-v-7a033bbb {
  color: #fff;
}
/* 录音波形动画 */
.record-wave.data-v-7a033bbb {
  position: absolute;
  right: 16rpx;
  display: flex;
  gap: 4rpx;
  align-items: center;
}
.wave-dot.data-v-7a033bbb {
  width: 6rpx;
  height: 6rpx;
  background: #fff;
  border-radius: 50%;
  -webkit-animation: wave-data-v-7a033bbb 1.4s ease-in-out infinite both;
          animation: wave-data-v-7a033bbb 1.4s ease-in-out infinite both;
}
.wave-dot.data-v-7a033bbb:nth-child(2) {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}
.wave-dot.data-v-7a033bbb:nth-child(3) {
  -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
}
@keyframes wave-data-v-7a033bbb {
0%,
  80%,
  100% {
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    opacity: 0.5;
}
40% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
    opacity: 1;
}
}
.input-actions.data-v-7a033bbb {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.action-btn.data-v-7a033bbb {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 36rpx;
  background: #f5f5f5;
  transition: all 0.2s ease;
}
.action-btn.data-v-7a033bbb:active {
  background: #e8e8e8;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.send-btn.data-v-7a033bbb {
  width: 72rpx;
  height: 72rpx;
  background: #2979ff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.send-btn.can-send.data-v-7a033bbb {
  background: #2979ff;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.3);
}
.send-btn.can-send.data-v-7a033bbb:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: #1e6bd8;
}
.send-btn.sending.data-v-7a033bbb {
  background: #999;
  pointer-events: none;
}
.send-btn.data-v-7a033bbb:not(.can-send) {
  background: #ccc;
  pointer-events: none;
}

