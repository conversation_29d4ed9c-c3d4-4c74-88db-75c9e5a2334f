@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.post-card.data-v-92e3cfc8 {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s ease, -webkit-transform 0.2s ease;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  transition: transform 0.2s ease, box-shadow 0.2s ease, -webkit-transform 0.2s ease;
}
.post-card.data-v-92e3cfc8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
}
.cover-container.data-v-92e3cfc8 {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}
.cover-image.data-v-92e3cfc8 {
  width: 100%;
  height: 100%;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.post-card:hover .cover-image.data-v-92e3cfc8 {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.image-placeholder.data-v-92e3cfc8 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.placeholder-text.data-v-92e3cfc8 {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
.post-title.data-v-92e3cfc8 {
  padding: 12px 12px 8px;
}
.title-text.data-v-92e3cfc8 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.post-footer.data-v-92e3cfc8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px 12px;
}
.user-info.data-v-92e3cfc8 {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.user-info.data-v-92e3cfc8:active {
  opacity: 0.7;
}
.username.data-v-92e3cfc8 {
  font-size: 12px;
  color: #666;
  margin-left: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.like-info.data-v-92e3cfc8 {
  display: flex;
  align-items: center;
  gap: 4px;
}
.like-count.data-v-92e3cfc8 {
  font-size: 12px;
  color: #999;
  font-weight: 500;
}
/* 私密标识 */
.private-badge.data-v-92e3cfc8 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.private-text.data-v-92e3cfc8 {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
}
/* 草稿标识 */
.draft-badge.data-v-92e3cfc8 {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(255, 152, 0, 0.8);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.draft-text.data-v-92e3cfc8 {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
}
/* 响应式适配 */
@media screen and (max-width: 375px) {
.cover-container.data-v-92e3cfc8 {
    height: 140px;
}
.title-text.data-v-92e3cfc8 {
    font-size: 13px;
}
.username.data-v-92e3cfc8,
  .like-count.data-v-92e3cfc8 {
    font-size: 11px;
}
}

