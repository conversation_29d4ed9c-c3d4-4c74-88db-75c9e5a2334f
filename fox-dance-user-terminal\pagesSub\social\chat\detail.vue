<template>
  <view class="chat-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <view class="header-left">
          <view class="avatar-container">
            <u-avatar
              :src="otherUserAvatar"
              size="50"
              class="header-avatar"
              @click="viewUserProfile"
            ></u-avatar>
            <view v-if="isOnline" class="online-indicator"></view>
          </view>
          <view class="chat-info" @click="viewUserProfile">
            <view class="name-container">
              <text class="chat-name">{{ chatName }}</text>
            </view>
          </view>
        </view>
        <view class="header-actions">
          <u-icon name="phone" size="24" color="#333" @click="makeCall"></u-icon>
          <u-icon name="more-dot-fill" size="24" color="#333" @click="showMoreActions"></u-icon>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view
      class="message-list"
      scroll-y
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
    >
      <view class="message-item" v-for="message in messageList" :key="message.id">
        <!-- 时间分割线 -->
        <view v-if="message.showTime" class="time-divider">
          <text class="time-text">{{ formatMessageTime(message.timestamp) }}</text>
        </view>

        <!-- 消息内容 -->
        <view class="message-wrapper" :class="{ 'is-mine': message.isMine }">
          <!-- 对方消息：左边头像，右边内容 -->
          <template v-if="!message.isMine">
            <u-avatar
              :src="message.avatar"
              size="64"
              class="message-avatar"
              @click="viewUserProfile"
            ></u-avatar>

            <view class="message-content">
              <!-- 文字消息 -->
              <view v-if="message.type === 'text'" class="message-bubble text-bubble">
                <text class="message-text">{{ message.content }}</text>
              </view>

              <!-- 图片消息 -->
              <view
                v-else-if="message.type === 'image'"
                class="message-bubble image-bubble"
                @click="previewImage(message.content)"
              >
                <image :src="message.content" class="message-image" mode="aspectFill" />
              </view>

              <!-- 语音消息 -->
              <view
                v-else-if="message.type === 'voice'"
                class="message-bubble voice-bubble"
                @click="playVoice(message)"
              >
                <u-icon name="volume-fill" size="16" color="#fff"></u-icon>
                <text class="voice-duration">{{ message.duration }}''</text>
                <view v-if="message.isPlaying" class="voice-animation">
                  <view class="wave"></view>
                  <view class="wave"></view>
                  <view class="wave"></view>
                </view>
              </view>
            </view>
          </template>

          <!-- 我的消息：左边内容，右边头像 -->
          <template v-else>
            <view class="message-content">
              <!-- 文字消息 -->
              <view v-if="message.type === 'text'" class="message-bubble text-bubble mine">
                <text class="message-text">{{ message.content }}</text>
              </view>

              <!-- 图片消息 -->
              <view
                v-else-if="message.type === 'image'"
                class="message-bubble image-bubble"
                @click="previewImage(message.content)"
              >
                <image :src="message.content" class="message-image" mode="aspectFill" />
              </view>

              <!-- 语音消息 -->
              <view
                v-else-if="message.type === 'voice'"
                class="message-bubble voice-bubble mine"
                @click="playVoice(message)"
              >
                <u-icon name="volume-fill" size="16" color="#fff"></u-icon>
                <text class="voice-duration">{{ message.duration }}''</text>
                <view v-if="message.isPlaying" class="voice-animation">
                  <view class="wave"></view>
                  <view class="wave"></view>
                  <view class="wave"></view>
                </view>
              </view>

              <!-- 消息状态 -->
              <view class="message-status">
                <u-icon v-if="message.status === 'sending'" name="loading" size="12" color="#999"></u-icon>
                <u-icon
                  v-else-if="message.status === 'sent'"
                  name="checkmark"
                  size="12"
                  color="#999"
                ></u-icon>
                <u-icon
                  v-else-if="message.status === 'read'"
                  name="checkmark-done"
                  size="12"
                  color="#2979ff"
                ></u-icon>
                <u-icon
                  v-else-if="message.status === 'failed'"
                  name="close-circle"
                  size="12"
                  color="#ff4757"
                  @click="resendMessage(message)"
                ></u-icon>
              </view>
            </view>
          </template>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <!-- 扩展功能面板 -->
      <view v-if="showExtensions" class="extensions-panel">
        <view class="extension-grid">
          <view class="extension-item" @click="chooseImage">
            <view class="extension-icon photo">
              <u-icon name="camera" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">照片</text>
          </view>

          <view class="extension-item" @click="startVoiceRecord">
            <view class="extension-icon voice">
              <u-icon name="mic" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">语音</text>
          </view>

          <view class="extension-item" @click="chooseLocation">
            <view class="extension-icon location">
              <u-icon name="map-pin" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">位置</text>
          </view>

          <view class="extension-item" @click="chooseFile">
            <view class="extension-icon file">
              <u-icon name="folder" color="#fff" size="24"></u-icon>
            </view>
            <text class="extension-text">文件</text>
          </view>
        </view>
      </view>

      <!-- 表情面板 -->
      <view v-if="showEmojis" class="emoji-panel">
        <scroll-view class="emoji-scroll" scroll-y>
          <view class="emoji-grid">
            <text
              v-for="emoji in emojiList"
              :key="emoji"
              class="emoji-item"
              @click="insertEmoji(emoji)"
            >{{ emoji }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 输入栏 -->
      <view class="input-bar" :class="{ 'input-focused': inputFocused }">
        <!-- 语音按钮 -->
        <view class="voice-btn" @click="toggleVoiceMode" v-if="!inputText && !voiceMode">
          <u-icon name="mic" size="22" color="#666"></u-icon>
        </view>

        <!-- 文本输入区域 -->
        <view class="input-wrapper" v-if="!voiceMode" :class="{ 'has-text': inputText }">
          <textarea
            v-model="inputText"
            class="input-textarea"
            placeholder="输入消息..."
            :maxlength="500"
            :auto-height="true"
            :cursor-spacing="10"
            :adjust-position="true"
            :show-confirm-bar="false"
            :disable-default-padding="true"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @input="onInputChange"
            @linechange="onLineChange"
            @confirm="onInputConfirm"
          />

          <!-- 字数统计 -->
          <view class="char-count" v-if="inputText && inputText.length > 400">
            <text :class="{ 'over-limit': inputText.length > 500 }">{{ inputText.length }}/500</text>
          </view>
        </view>

        <!-- 语音录制按钮 -->
        <view
          v-else
          class="voice-record-btn"
          :class="{
            'recording': isRecording,
            'record-ready': !isRecording
          }"
          @touchstart="startRecord"
          @touchend="stopRecord"
          @touchcancel="cancelRecord"
        >
          <view class="record-icon">
            <u-icon
              :name="isRecording ? 'pause-circle-fill' : 'mic-fill'"
              :color="isRecording ? '#fff' : '#666'"
              size="20"
            ></u-icon>
          </view>
          <text class="record-text">{{ isRecording ? '松开发送' : '按住说话' }}</text>
          <view v-if="isRecording" class="record-wave">
            <view class="wave-dot" v-for="i in 3" :key="i"></view>
          </view>
        </view>

        <!-- 操作按钮区域 -->
        <view class="input-actions">
          <!-- 表情按钮 -->
          <view class="action-btn" @click="toggleEmojis">
            <u-icon name="emoji" size="22" :color="showEmojis ? '#2979ff' : '#666'"></u-icon>
          </view>

          <!-- 扩展功能按钮 -->
          <view class="action-btn" @click="toggleExtensions" v-if="!inputText && !voiceMode">
            <u-icon name="plus" size="22" :color="showExtensions ? '#2979ff' : '#666'"></u-icon>
          </view>

          <!-- 发送按钮 -->
          <view
            class="send-btn"
            :class="{
              'can-send': canSendMessage,
              'sending': isSending
            }"
            @click="sendMessage"
            v-if="inputText || voiceMode"
          >
            <u-icon v-if="!isSending" name="send" color="#fff" size="18"></u-icon>
            <u-loading v-else mode="circle" size="16" color="#fff"></u-loading>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getConversationMessages,
  sendMessage,
  markMessageAsRead,
  clearChatHistory,
  reportUser
} from "@/utils/socialApi.js";

export default {
  name: "ChatDetail",
  data() {
    return {
      chatId: "",
      chatName: "",
      otherUserAvatar: "https://picsum.photos/100/100?random=800",
      isOnline: true,
      messageList: [],
      inputText: "",
      scrollTop: 0,
      showExtensions: false,
      showEmojis: false,
      voiceMode: false,
      isRecording: false,
      inputFocused: false,
      isSending: false,
      isTyping: false,
      typingTimer: null,
      currentUser: {
        avatar: ""
      },
      // 语音录制相关
      recorderManager: null,
      voiceRecordPath: "",
      voiceRecordDuration: 0,
      recordStartTime: 0,
      currentPlayingVoice: null,
      innerAudioContext: null,
      emojiList: [
        "😀",
        "😃",
        "😄",
        "😁",
        "😆",
        "😅",
        "😂",
        "🤣",
        "😊",
        "😇",
        "🙂",
        "🙃",
        "😉",
        "😌",
        "😍",
        "🥰",
        "😘",
        "😗",
        "😙",
        "😚",
        "😋",
        "😛",
        "😝",
        "😜",
        "🤪",
        "🤨",
        "🧐",
        "🤓",
        "😎",
        "🤩",
        "🥳",
        "😏"
      ],
      // uview输入框自定义样式
      inputCustomStyle: {
        backgroundColor: "transparent",
        fontSize: "32rpx",
        lineHeight: "1.4",
        minHeight: "40rpx",
        maxHeight: "200rpx",
        padding: "0",
        color: "#333"
      },
      // placeholder样式
      placeholderStyle: "color: #999; font-size: 32rpx;",

      // WebSocket相关
      socketTask: null,
      isConnected: false,
      reconnectTimer: null,
      reconnectCount: 0,
      maxReconnectCount: 5,
      heartbeatTimer: null
    };
  },
  onLoad(options) {
    console.log("聊天页面参数:", options);

    // 支持从用户主页跳转的参数
    if (options.userId) {
      this.chatId = options.userId;
      this.chatName = decodeURIComponent(options.nickname || "用户");
      this.otherUserAvatar = options.avatar
        ? decodeURIComponent(options.avatar)
        : "https://picsum.photos/100/100?random=800";
    } else {
      // 兼容原有的参数格式
      this.chatId = options.id;
      this.chatName = options.name || "聊天";
    }

    console.log("聊天对象信息:", {
      chatId: this.chatId,
      chatName: this.chatName,
      otherUserAvatar: this.otherUserAvatar
    });

    // 初始化语音录制管理器
    this.initRecorderManager();

    // 加载当前用户头像
    this.loadCurrentUserInfo();

    this.loadMessages();
    this.connectWebSocket();
  },

  computed: {
    canSendMessage() {
      return (
        this.inputText.trim().length > 0 &&
        this.inputText.length <= 500 &&
        !this.isSending
      );
    }
  },

  onUnload() {
    this.disconnectWebSocket();
    this.cleanupAudio();
  },

  onHide() {
    this.disconnectWebSocket();
    this.cleanupAudio();
  },

  onShow() {
    if (!this.isConnected) {
      this.connectWebSocket();
    }
  },

  methods: {
    // ==================== WebSocket相关方法 ====================

    connectWebSocket() {
      if (this.isConnected || this.socketTask) {
        return;
      }

      console.log("开始连接WebSocket...");

      // 获取当前用户ID和token
      const currentUserId = uni.getStorageSync("userid");
      const token = uni.getStorageSync("token");

      if (!currentUserId || !token) {
        console.error("缺少用户ID或token，无法连接WebSocket");
        return;
      }

      // 检查开发者工具设置
      console.log("📱 当前环境信息:");
      console.log("- 平台:", uni.getSystemInfoSync().platform);
      console.log("- 环境:", process.env.NODE_ENV);

      // 构建WebSocket URL
      const wsUrl = `${this.getWebSocketUrl()}?userId=${currentUserId}&token=${token}`;
      console.log("🔗 尝试连接WebSocket:", wsUrl);

      // 尝试不同的连接方式
      this.socketTask = uni.connectSocket({
        url: wsUrl,
        protocols: [], // 明确指定协议
        success: () => {
          console.log("✅ WebSocket连接请求发送成功");
        },
        fail: error => {
          console.error("❌ WebSocket连接失败:", error);
          console.error("连接URL:", wsUrl);
          console.error("用户ID:", currentUserId);
          console.error("Token:", token);
          console.error("错误详情:", JSON.stringify(error));

          // 尝试使用HTTP测试连接性
          this.testHttpConnection();

          // 显示用户友好的错误信息
          uni.showToast({
            title: "WebSocket连接失败",
            icon: "none",
            duration: 3000
          });

          this.scheduleReconnect();
        }
      });

      // 监听WebSocket连接打开
      this.socketTask.onOpen(() => {
        console.log("🎉 WebSocket连接已打开");
        this.isConnected = true;
        this.reconnectCount = 0;
        this.startHeartbeat();

        // 显示连接成功提示
        uni.showToast({
          title: "连接成功",
          icon: "success",
          duration: 2000
        });

        // 加入聊天房间
        this.joinChatRoom();
      });

      // 监听WebSocket消息
      this.socketTask.onMessage(res => {
        console.log("收到WebSocket消息:", res.data);
        this.handleWebSocketMessage(res.data);
      });

      // 监听WebSocket连接关闭
      this.socketTask.onClose(() => {
        console.log("WebSocket连接已关闭");
        this.isConnected = false;
        this.stopHeartbeat();
        this.scheduleReconnect();
      });

      // 监听WebSocket错误
      this.socketTask.onError(error => {
        console.error("WebSocket错误:", error);
        this.isConnected = false;
        this.scheduleReconnect();
      });
    },

    // 模拟WebSocket连接（用于测试）
    simulateWebSocketConnection() {
      console.log("🔧 模拟WebSocket连接成功");
      this.isConnected = true;
      this.reconnectCount = 0;

      // 模拟连接成功
      uni.showToast({
        title: "连接成功（模拟）",
        icon: "success",
        duration: 2000
      });

      // 模拟接收消息（用于测试）
      setTimeout(() => {
        this.simulateReceiveMessage();
      }, 3000);
    },

    // 模拟接收消息
    simulateReceiveMessage() {
      const mockMessage = {
        id: Date.now(),
        senderId: this.chatId, // 对方ID
        receiverId: uni.getStorageSync("userid"),
        messageType: 1,
        content: "这是一条模拟接收的消息，用于测试聊天界面功能！",
        createTime: new Date().toISOString(),
        isRead: false
      };

      this.handleNewMessage(mockMessage);
      console.log("📨 模拟接收到消息:", mockMessage);
    },

    // 模拟对方回复
    simulateReply(originalMessage) {
      const replies = [
        "收到你的消息了！",
        "好的，我知道了",
        "谢谢你的分享",
        "这个想法不错",
        "我也是这么想的",
        `关于"${originalMessage}"，我觉得很有意思`,
        "让我想想...",
        "同意你的观点"
      ];

      const randomReply = replies[Math.floor(Math.random() * replies.length)];

      const replyMessage = {
        id: Date.now(),
        senderId: this.chatId, // 对方ID
        receiverId: uni.getStorageSync("userid"),
        messageType: 1,
        content: randomReply,
        createTime: new Date().toISOString(),
        isRead: false
      };

      this.handleNewMessage(replyMessage);
      console.log("🤖 模拟对方回复:", randomReply);
    },

    // 测试HTTP连接
    testHttpConnection() {
      const testUrl = "http://192.168.1.21:8101/api/";
      console.log("🔍 测试HTTP连接:", testUrl);

      uni.request({
        url: testUrl,
        method: "GET",
        success: res => {
          console.log("✅ HTTP连接成功:", res);
        },
        fail: error => {
          console.error("❌ HTTP连接失败:", error);
        }
      });
    },

    disconnectWebSocket() {
      console.log("断开WebSocket连接");

      this.isConnected = false;
      this.stopHeartbeat();

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.socketTask) {
        this.socketTask.close();
        this.socketTask = null;
      }
    },

    scheduleReconnect() {
      if (this.reconnectCount >= this.maxReconnectCount) {
        console.log("达到最大重连次数，停止重连");
        return;
      }

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      const delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒
      console.log(`${delay}ms后尝试重连 (第${this.reconnectCount + 1}次)`);

      this.reconnectTimer = setTimeout(() => {
        this.reconnectCount++;
        this.connectWebSocket();
      }, delay);
    },

    startHeartbeat() {
      this.stopHeartbeat();
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected && this.socketTask) {
          this.socketTask.send({
            data: JSON.stringify({
              type: "heartbeat",
              timestamp: Date.now()
            })
          });
        }
      }, 30000); // 30秒心跳
    },

    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },

    getWebSocketUrl() {
      // 根据环境返回WebSocket URL
      const systemInfo = uni.getSystemInfoSync();

      // 检查是否为开发环境
      if (
        process.env.NODE_ENV === "development" ||
        systemInfo.platform === "devtools"
      ) {
        // 开发环境使用本地WebSocket服务（端口8102）
        console.log("🔧 开发环境，使用本地WebSocket服务");
        return "ws://localhost:8101/api/ws/chat";
      } else {
        // 生产环境使用服务器WebSocket
        console.log("🌐 生产环境，使用服务器WebSocket服务");
        return "wss://admin.foxdance.com.cn/ws/chat";
      }
    },

    joinChatRoom() {
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "join",
            chatId: this.chatId,
            userId: uni.getStorageSync("userid")
          })
        });
      }
    },

    handleWebSocketMessage(data) {
      try {
        const message = JSON.parse(data);
        console.log("处理WebSocket消息:", message);

        switch (message.type) {
          case "message":
            this.handleNewMessage(message.data);
            break;
          case "typing":
            this.handleTypingStatus(message.data);
            break;
          case "read":
            this.handleMessageRead(message.data);
            break;
          case "online":
            this.handleOnlineStatus(message.data);
            break;
          default:
            console.log("未知消息类型:", message.type);
        }
      } catch (error) {
        console.error("解析WebSocket消息失败:", error);
      }
    },

    handleNewMessage(messageData) {
      // 只处理对方发送的消息
      if (messageData.senderId != this.chatId) {
        return;
      }

      const message = {
        id: messageData.id,
        type: this.getMessageTypeString(messageData.messageType),
        content: messageData.content,
        isMine: false,
        avatar: this.otherUserAvatar,
        timestamp: new Date(messageData.createTime),
        showTime: this.shouldShowTime(messageData.createTime),
        status: "sent",
        isPlaying: false
      };

      // 如果是语音消息，添加duration字段
      if (messageData.messageType === 3) {
        // 优先使用mediaDuration字段，否则从content中提取时长
        if (messageData.mediaDuration) {
          message.duration = messageData.mediaDuration;
        } else {
          const durationMatch = messageData.content.match(/(\d+)秒/);
          message.duration = durationMatch ? parseInt(durationMatch[1]) : 1;
        }
        // 如果有mediaUrl，使用mediaUrl作为播放地址
        if (messageData.mediaUrl) {
          message.content = messageData.mediaUrl;
        }
      }

      this.messageList.push(message);
      this.scrollToBottom();

      // 播放消息提示音
      this.playMessageSound();
    },

    handleTypingStatus(data) {
      // 处理对方正在输入状态
      console.log("对方正在输入:", data);
    },

    handleMessageRead(data) {
      // 处理消息已读状态
      const messageIndex = this.messageList.findIndex(
        msg => msg.id === data.messageId
      );
      if (messageIndex !== -1) {
        this.messageList[messageIndex].status = "read";
      }
    },

    handleOnlineStatus(data) {
      // 处理在线状态
      this.isOnline = data.isOnline;
    },

    shouldShowTime(timestamp) {
      if (this.messageList.length === 0) return true;

      const lastMessage = this.messageList[this.messageList.length - 1];
      const timeDiff = new Date(timestamp) - lastMessage.timestamp;
      return timeDiff > 300000; // 5分钟
    },

    playMessageSound() {
      // 播放消息提示音
      try {
        const innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.src = "/static/sounds/message.mp3";
        innerAudioContext.play();
      } catch (error) {
        console.log("播放提示音失败:", error);
      }
    },

    // ==================== 原有方法 ====================

    loadCurrentUserInfo() {
      // 从缓存中获取当前用户信息
      const userInfo = uni.getStorageSync("user");
      if (userInfo && userInfo.data) {
        this.currentUser.avatar = userInfo.data.avatar
          ? "https://file.foxdance.com.cn" + userInfo.data.avatar
          : "/static/images/toux.png";
      } else {
        this.currentUser.avatar = "/static/images/toux.png";
      }

      console.log("当前用户头像:", this.currentUser.avatar);
    },

    async loadMessages() {
      console.log("开始加载聊天消息，对方用户ID:", this.chatId);

      try {
        // 获取当前用户ID
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) {
          console.error("当前用户ID无效，无法加载消息");
          uni.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }

        console.log("当前用户ID:", currentUserId, "对方用户ID:", this.chatId);

        const result = await getConversationMessages(this.chatId, {
          current: 1,
          size: 50
        });

        console.log("聊天消息API返回:", result);

        // 处理API返回的数据格式
        let messages = [];
        if (
          result &&
          result.code === 0 &&
          result.data &&
          Array.isArray(result.data)
        ) {
          messages = result.data;
        } else if (result && Array.isArray(result)) {
          messages = result;
        } else {
          console.log("没有找到聊天消息或格式不正确");
          messages = [];
        }

        if (messages.length > 0) {
          this.messageList = messages
            .map((message, index) => {
              const prevMessage = messages[index - 1];
              const showTime =
                !prevMessage ||
                new Date(message.createTime) -
                  new Date(prevMessage.createTime) >
                  300000; // 5分钟

              const messageObj = {
                id: message.id,
                type: this.getMessageTypeString(message.messageType),
                content: message.content,
                isMine: message.senderId === currentUserId,
                avatar:
                  message.senderId === currentUserId
                    ? this.currentUser.avatar
                    : this.otherUserAvatar,
                timestamp: new Date(message.createTime),
                showTime: showTime,
                status: message.isRead === 1 ? "read" : "sent"
              };

              // 如果是语音消息，添加语音相关字段
              if (message.messageType === 3) {
                messageObj.isPlaying = false;
                // 优先使用mediaDuration字段，否则从content中提取时长
                if (message.mediaDuration) {
                  messageObj.duration = message.mediaDuration;
                } else {
                  const durationMatch = message.content.match(/(\d+)秒/);
                  messageObj.duration = durationMatch ? parseInt(durationMatch[1]) : 1;
                }
                // 如果有mediaUrl，使用mediaUrl作为播放地址
                if (message.mediaUrl) {
                  messageObj.content = message.mediaUrl;
                }
              }

              return messageObj;
            })
            .reverse(); // 消息按时间正序显示

          console.log("消息列表处理完成，消息数量:", this.messageList.length);

          // 标记消息已读
          await this.markAllMessagesAsRead();
        } else {
          console.log("没有历史消息，显示空聊天界面");
          this.messageList = [];
        }
      } catch (error) {
        console.error("加载消息失败:", error);
        uni.showToast({
          title: "消息加载失败",
          icon: "none"
        });
        this.messageList = [];
      }

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 获取当前用户ID
    getCurrentUserId() {
      const userId = uni.getStorageSync("userid");
      return userId ? Number(userId) : null;
    },

    // 将消息类型数字转换为字符串
    getMessageTypeString(messageType) {
      const typeMap = {
        1: "text",
        2: "image",
        3: "voice",
        4: "video"
      };
      return typeMap[messageType] || "text";
    },

    // 标记所有消息已读
    async markAllMessagesAsRead() {
      try {
        const currentUserId = this.getCurrentUserId();
        if (!currentUserId) return;

        await markMessageAsRead({
          senderId: this.chatId,
          receiverId: currentUserId
        });
        console.log("消息已标记为已读");
      } catch (error) {
        console.error("标记消息已读失败:", error);
      }
    },

    formatMessageTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      if (diff < 86400000) {
        // 今天
        return `${date
          .getHours()
          .toString()
          .padStart(2, "0")}:${date
          .getMinutes()
          .toString()
          .padStart(2, "0")}`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date
          .getHours()
          .toString()
          .padStart(2, "0")}:${date
          .getMinutes()
          .toString()
          .padStart(2, "0")}`;
      }
    },

    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    },

    async sendMessage() {
      if (!this.canSendMessage) return;

      const messageContent = this.inputText.trim();
      const tempId = Date.now();

      // 设置发送状态
      this.isSending = true;

      // 停止输入状态提示
      this.stopTypingIndicator();

      const message = {
        id: tempId,
        type: "text",
        content: messageContent,
        isMine: true,
        timestamp: new Date(),
        showTime: this.shouldShowTime(new Date()),
        status: "sending"
      };

      this.messageList.push(message);
      this.inputText = "";
      this.scrollToBottom();

      try {
        // 获取当前用户ID
        const currentUserId = uni.getStorageSync("userid");

        // 1. 先通过API发送消息到服务器
        const result = await sendMessage({
          receiverId: Number(this.chatId),
          messageType: 1, // 1表示文本消息
          content: messageContent
        });

        console.log("发送消息API返回:", result);

        if (result && result.code === 0) {
          // 更新消息状态和ID
          message.id = result.data.id || tempId;
          message.status = "sent";

          // 2. 通过WebSocket实时推送消息
          if (this.isConnected && this.socketTask) {
            this.socketTask.send({
              data: JSON.stringify({
                type: "message",
                data: {
                  id: message.id,
                  senderId: currentUserId,
                  receiverId: this.chatId,
                  messageType: 1,
                  content: messageContent,
                  createTime: new Date().toISOString()
                }
              })
            });
            console.log("消息已通过WebSocket发送");
          }

          console.log("消息发送成功:", result.data);
        } else {
          message.status = "failed";
          console.error("消息发送失败:", result);
          uni.showToast({
            title: result?.message || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("发送消息失败:", error);
        message.status = "failed";
        uni.showToast({
          title: "发送失败",
          icon: "none"
        });
      } finally {
        // 重置发送状态
        this.isSending = false;
      }
    },

    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: res => {
          const message = {
            id: Date.now(),
            type: "image",
            content: res.tempFilePaths[0],
            isMine: true,
            timestamp: new Date(),
            showTime: false,
            status: "sending"
          };

          this.messageList.push(message);
          this.showExtensions = false;
          this.scrollToBottom();

          // 模拟上传成功
          setTimeout(() => {
            message.status = "sent";
          }, 1000);
        }
      });
    },

    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },

    startVoiceRecord() {
      this.voiceMode = true;
      this.showExtensions = false;
    },

    toggleVoiceMode() {
      this.voiceMode = !this.voiceMode;
    },

    // ==================== 语音录制相关方法 ====================

    // 初始化录音管理器
    initRecorderManager() {
      this.recorderManager = uni.getRecorderManager();

      // 录音开始事件
      this.recorderManager.onStart(() => {
        console.log("录音开始");
        this.recordStartTime = Date.now();
      });

      // 录音结束事件
      this.recorderManager.onStop(res => {
        console.log("录音结束", res);
        this.voiceRecordPath = res.tempFilePath;
        this.voiceRecordDuration = Math.floor(res.duration / 1000); // 转换为秒

        // 发送语音消息
        this.sendVoiceMessage(res.tempFilePath, this.voiceRecordDuration);
      });

      // 录音错误事件
      this.recorderManager.onError(err => {
        console.error("录音错误:", err);
        this.isRecording = false;
        uni.showToast({
          title: "录音失败",
          icon: "none"
        });
      });
    },

    startRecord() {
      console.log("开始录音");
      this.isRecording = true;

      // 检查录音权限
      uni.authorize({
        scope: "scope.record",
        success: () => {
          // 开始录音
          this.recorderManager.start({
            duration: 60000, // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: "mp3",
            frameSize: 50
          });
        },
        fail: () => {
          this.isRecording = false;
          uni.showModal({
            title: "录音权限",
            content: "需要录音权限才能发送语音消息",
            confirmText: "去设置",
            success: res => {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
    },

    stopRecord() {
      if (!this.isRecording) return;

      console.log("停止录音");
      this.isRecording = false;

      // 检查录音时长
      const recordDuration = Date.now() - this.recordStartTime;
      if (recordDuration < 1000) {
        uni.showToast({
          title: "录音时间太短",
          icon: "none"
        });
        this.recorderManager.stop();
        return;
      }

      // 停止录音
      this.recorderManager.stop();
    },

    cancelRecord() {
      console.log("取消录音");
      this.isRecording = false;
      this.recorderManager.stop();
    },

    // 发送语音消息
    async sendVoiceMessage(tempFilePath, duration) {
      console.log("准备发送语音消息:", { tempFilePath, duration });

      // 创建临时消息显示在界面上
      const tempMessage = {
        id: Date.now(),
        type: "voice",
        content: "",
        duration: duration,
        isMine: true,
        timestamp: new Date(),
        showTime: this.shouldShowTime(new Date()),
        status: "sending",
        isPlaying: false
      };

      this.messageList.push(tempMessage);
      this.scrollToBottom();

      try {
        // 1. 上传语音文件
        const uploadResult = await this.uploadVoiceFile(tempFilePath);
        console.log("语音文件上传成功:", uploadResult);

        // 2. 发送消息到服务器
        const currentUserId = uni.getStorageSync("userid");
        const result = await sendMessage({
          receiverId: Number(this.chatId),
          messageType: 3, // 3表示语音消息
          content: `语音消息 ${duration}秒`,
          mediaUrl: uploadResult.url,
          mediaDuration: duration,
          mediaSize: uploadResult.size || 0
        });

        console.log("语音消息发送API返回:", result);

        if (result && result.code === 0) {
          // 更新消息状态和ID
          tempMessage.id = result.data.id || tempMessage.id;
          tempMessage.status = "sent";
          tempMessage.content = uploadResult.url; // 存储语音文件URL

          // 通过WebSocket实时推送消息
          if (this.isConnected && this.socketTask) {
            this.socketTask.send({
              data: JSON.stringify({
                type: "message",
                data: {
                  id: tempMessage.id,
                  senderId: currentUserId,
                  receiverId: this.chatId,
                  messageType: 3,
                  content: uploadResult.url,
                  mediaUrl: uploadResult.url,
                  mediaDuration: duration,
                  createTime: new Date().toISOString()
                }
              })
            });
            console.log("语音消息已通过WebSocket发送");
          }

          console.log("语音消息发送成功");
        } else {
          tempMessage.status = "failed";
          console.error("语音消息发送失败:", result);
          uni.showToast({
            title: result?.message || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("发送语音消息失败:", error);
        tempMessage.status = "failed";
        uni.showToast({
          title: "发送失败",
          icon: "none"
        });
      }
    },

    // 上传语音文件
    uploadVoiceFile(tempFilePath) {
      return new Promise((resolve, reject) => {
        const token = uni.getStorageSync("token");
        const currentUserId = uni.getStorageSync("userid");
        const baseUrl = this.getApiBaseUrl();

        console.log("开始上传语音文件:", tempFilePath);

        uni.uploadFile({
          url: `${baseUrl}/api/upload/voice`,
          filePath: tempFilePath,
          name: "file",
          formData: {
            driver: "cos"
          },
          header: {
            bausertoken: token,
            userid: currentUserId
          },
          success: uploadFileRes => {
            console.log("语音文件上传响应:", uploadFileRes);

            try {
              const result = JSON.parse(uploadFileRes.data);
              console.log("语音文件上传结果:", result);

              if (result.code === 0 && result.data) {
                resolve({
                  url: result.data.url || result.data,
                  size: result.data.size || 0
                });
              } else {
                console.error("语音文件上传失败:", result);
                reject(new Error(result.message || "上传失败"));
              }
            } catch (parseError) {
              console.error("解析上传响应失败:", parseError);
              reject(new Error("解析响应失败"));
            }
          },
          fail: error => {
            console.error("语音文件上传请求失败:", error);
            reject(new Error("上传请求失败"));
          }
        });
      });
    },

    // 播放语音消息
    playVoice(message) {
      console.log("播放语音消息:", message);

      // 停止当前播放的语音
      if (this.currentPlayingVoice && this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.currentPlayingVoice.isPlaying = false;
      }

      // 停止其他语音播放状态
      this.messageList.forEach(msg => {
        if (msg.type === "voice") {
          msg.isPlaying = false;
        }
      });

      // 如果点击的是正在播放的语音，则停止播放
      if (this.currentPlayingVoice === message) {
        this.currentPlayingVoice = null;
        return;
      }

      // 开始播放新的语音
      message.isPlaying = true;
      this.currentPlayingVoice = message;

      // 创建音频上下文
      this.innerAudioContext = uni.createInnerAudioContext();
      this.innerAudioContext.src = message.content; // content存储的是语音文件URL

      // 播放开始
      this.innerAudioContext.onPlay(() => {
        console.log("语音开始播放");
      });

      // 播放结束
      this.innerAudioContext.onEnded(() => {
        console.log("语音播放结束");
        message.isPlaying = false;
        this.currentPlayingVoice = null;
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy();
          this.innerAudioContext = null;
        }
      });

      // 播放错误
      this.innerAudioContext.onError(error => {
        console.error("语音播放错误:", error);
        message.isPlaying = false;
        this.currentPlayingVoice = null;
        uni.showToast({
          title: "播放失败",
          icon: "none"
        });
        if (this.innerAudioContext) {
          this.innerAudioContext.destroy();
          this.innerAudioContext = null;
        }
      });

      // 开始播放
      this.innerAudioContext.play();
    },

    // 清理音频资源
    cleanupAudio() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.innerAudioContext.destroy();
        this.innerAudioContext = null;
      }
      if (this.currentPlayingVoice) {
        this.currentPlayingVoice.isPlaying = false;
        this.currentPlayingVoice = null;
      }
    },

    // 获取API基础URL
    getApiBaseUrl() {
      // 根据环境返回不同的API地址
      const systemInfo = uni.getSystemInfoSync();

      if (
        process.env.NODE_ENV === "development" ||
        systemInfo.platform === "devtools"
      ) {
        return "http://localhost:8101";
      } else {
        return "https://admin.foxdance.com.cn";
      }
    },

    toggleEmojis() {
      this.showEmojis = !this.showEmojis;
      this.showExtensions = false;
    },

    toggleExtensions() {
      this.showExtensions = !this.showExtensions;
      this.showEmojis = false;
    },

    insertEmoji(emoji) {
      this.inputText += emoji;
    },

    onInputFocus() {
      console.log("输入框获得焦点");
      this.inputFocused = true;
      this.showEmojis = false;
      this.showExtensions = false;

      // 滚动到底部，确保输入框可见
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    onInputBlur() {
      console.log("输入框失去焦点");
      this.inputFocused = false;

      // 停止输入状态提示
      this.stopTypingIndicator();
    },

    onInputChange(e) {
      const value = e.detail.value;
      this.inputText = value;

      // 发送正在输入状态
      this.sendTypingIndicator();

      // 输入验证
      if (value.length > 500) {
        uni.showToast({
          title: "消息长度不能超过500字",
          icon: "none",
          duration: 1500
        });
      }
    },

    onLineChange(e) {
      // 处理行数变化，可以用于调整输入框高度
      console.log("输入框行数变化:", e.detail);
    },

    onInputConfirm() {
      // 处理键盘确认事件（如果需要支持回车发送）
      if (this.canSendMessage) {
        this.sendMessage();
      }
    },

    // 发送正在输入状态
    sendTypingIndicator() {
      if (this.isTyping) return;

      this.isTyping = true;

      // 通过WebSocket发送正在输入状态
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "typing",
            data: {
              userId: uni.getStorageSync("userid"),
              chatId: this.chatId,
              isTyping: true
            }
          })
        });
      }

      // 3秒后自动停止输入状态
      if (this.typingTimer) {
        clearTimeout(this.typingTimer);
      }

      this.typingTimer = setTimeout(() => {
        this.stopTypingIndicator();
      }, 3000);
    },

    // 停止正在输入状态
    stopTypingIndicator() {
      if (!this.isTyping) return;

      this.isTyping = false;

      if (this.typingTimer) {
        clearTimeout(this.typingTimer);
        this.typingTimer = null;
      }

      // 通过WebSocket发送停止输入状态
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({
            type: "typing",
            data: {
              userId: uni.getStorageSync("userid"),
              chatId: this.chatId,
              isTyping: false
            }
          })
        });
      }
    },

    goBack() {
      uni.navigateBack();
    },

    makeCall() {
      uni.makePhoneCall({
        phoneNumber: "10086"
      });
    },

    showMoreActions() {
      uni.showActionSheet({
        itemList: ["查看资料", "清空聊天记录", "举报"],
        success: res => {
          switch (res.tapIndex) {
            case 0:
              this.viewUserProfile();
              break;
            case 1:
              this.clearChatHistory();
              break;
            case 2:
              this.reportUser();
              break;
          }
        }
      });
    },

    // 查看用户资料
    viewUserProfile() {
      uni.navigateTo({
        url: `/pagesSub/social/user/profile?userId=${this.chatId}&name=${this.chatName}`
      });
    },

    // 清空聊天记录
    clearChatHistory() {
      uni.showModal({
        title: "清空聊天记录",
        content: "确定要清空与该用户的所有聊天记录吗？此操作不可恢复。",
        confirmText: "清空",
        confirmColor: "#ff4757",
        success: res => {
          if (res.confirm) {
            // 清空消息列表
            this.messageList = [];

            // 显示成功提示
            uni.showToast({
              title: "聊天记录已清空",
              icon: "success",
              duration: 2000
            });

            // 这里可以调用后端API清空服务器端的聊天记录
            this.clearChatHistoryFromServer();
          }
        }
      });
    },

    // 从服务器清空聊天记录
    async clearChatHistoryFromServer() {
      try {
        const result = await clearChatHistory(this.chatId);

        if (result && result.code === 0) {
          console.log("服务器聊天记录已清空");
        } else {
          console.error("清空聊天记录失败:", result?.message || "未知错误");
          uni.showToast({
            title: result?.message || "清空失败，请重试",
            icon: "error",
            duration: 2000
          });
        }
      } catch (error) {
        console.error("清空聊天记录失败:", error);
        uni.showToast({
          title: "清空失败，请重试",
          icon: "error",
          duration: 2000
        });
      }
    },

    // 举报用户
    reportUser() {
      const reportReasons = [
        "发送垃圾信息",
        "发送不当内容",
        "骚扰他人",
        "诈骗行为",
        "其他违规行为"
      ];

      uni.showActionSheet({
        itemList: reportReasons,
        success: res => {
          const reason = reportReasons[res.tapIndex];
          this.confirmReport(reason);
        }
      });
    },

    // 确认举报
    confirmReport(reason) {
      uni.showModal({
        title: "举报用户",
        content: `确定要举报该用户"${reason}"吗？我们会认真处理您的举报。`,
        confirmText: "举报",
        confirmColor: "#ff4757",
        success: res => {
          if (res.confirm) {
            // 提交举报
            this.submitReport(reason);
          }
        }
      });
    },

    // 提交举报到服务器
    async submitReport(reason) {
      // 显示加载提示
      uni.showLoading({
        title: "提交中..."
      });

      try {
        const reportData = {
          reportedUserId: this.chatId,
          reportedUserName: this.chatName,
          reason: reason,
          reportType: "chat",
          description: `在聊天中举报用户：${reason}`,
          timestamp: new Date().toISOString()
        };

        const result = await reportUser(reportData);

        uni.hideLoading();

        if (result && result.code === 0) {
          uni.showToast({
            title: "举报已提交",
            icon: "success",
            duration: 2000
          });

          // 记录举报成功
          console.log("举报提交成功:", result);
        } else {
          console.error("举报提交失败:", result?.message || "未知错误");
          uni.showToast({
            title: result?.message || "提交失败，请重试",
            icon: "error",
            duration: 2000
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error("举报提交失败:", error);
        uni.showToast({
          title: "提交失败，请重试",
          icon: "error",
          duration: 2000
        });
      }
    },

    loadMoreMessages() {
      // 加载更多历史消息
    },

    resendMessage(message) {
      message.status = "sending";
      setTimeout(() => {
        message.status = "sent";
      }, 500);
    },

    chooseLocation() {
      this.showExtensions = false;
      this.$u.toast("位置功能开发中");
    },

    chooseFile() {
      this.showExtensions = false;
      this.$u.toast("文件功能开发中");
    }
  }
};
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100vh;
  background: #f0f0f0;
  position: relative;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: var(--status-bar-height);
}

.header-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.header-avatar {
  cursor: pointer;
}

.chat-info {
  cursor: pointer;
  flex: 1;
}

.name-container {
  position: relative;
  display: inline-block;
}

.chat-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  padding-right: 20rpx;
}

.online-indicator {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
  width: 16rpx;
  height: 16rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 2rpx solid #fff;
  box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  animation: online-pulse 2s infinite;
}

@keyframes online-pulse {
  0% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  }
  50% {
    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);
  }
  100% {
    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);
  }
}

.header-actions {
  display: flex;
  gap: 32rpx;
}

.message-list {
  position: fixed;
  top: calc(88rpx + var(--status-bar-height) + 2rpx);
  bottom: 120rpx;
  left: 0;
  right: 0;
  padding: 32rpx;
  overflow-y: auto;
  width: auto;
}

.message-item {
  margin-bottom: 32rpx;
  margin-top: 32rpx;
}

.time-divider {
  text-align: center;
  margin-bottom: 32rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  background: rgba(0, 0, 0, 0.1);
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
}

.message-wrapper {
  display: flex;
  align-items: flex-end;
}

.message-wrapper.is-mine {
  justify-content: flex-end;
}

.message-avatar {
  margin: 0 16rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.message-avatar:hover {
  transform: scale(1.05);
}

.message-content {
  display: flex;
  flex-direction: column;
  width: auto;
}

/* 对方消息：内容左对齐 */
.message-wrapper:not(.is-mine) .message-content {
  align-items: flex-start;
}

/* 我的消息：内容右对齐 */
.message-wrapper.is-mine .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 24rpx 32rpx;
  border-radius: 36rpx;
  margin-bottom: 8rpx;
}

.text-bubble {
  background: #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.text-bubble.mine {
  background: #2979ff;
}

.message-text {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}

.mine .message-text {
  color: #fff;
}

.image-bubble {
  padding: 0;
  overflow: hidden;
  background: transparent;
}

.message-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 24rpx;
}

.voice-bubble {
  background: #2979ff;
  display: flex;
  align-items: center;
  min-width: 160rpx;
  position: relative;
}

.voice-duration {
  color: #fff;
  font-size: 28rpx;
  margin-left: 16rpx;
}

.voice-animation {
  display: flex;
  gap: 4rpx;
  margin-left: 16rpx;
}

.wave {
  width: 4rpx;
  height: 24rpx;
  background: #fff;
  animation: wave 1s infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.1s;
}

.wave:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%,
  100% {
    height: 8rpx;
  }
  50% {
    height: 24rpx;
  }
}

.message-status {
  margin-top: 8rpx;
}

.input-area {
  background: #fff;
  border-top: 2rpx solid #e4e7ed;
}

.extensions-panel,
.emoji-panel {
  height: 400rpx;
  border-bottom: 2rpx solid #e4e7ed;
}

.extension-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 40rpx;
  gap: 40rpx;
}

.extension-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 30rpx);
}

.extension-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.extension-icon.photo {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.extension-icon.voice {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.extension-icon.location {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.extension-icon.file {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.extension-text {
  font-size: 24rpx;
  color: #666;
}

.emoji-scroll {
  height: 100%;
  padding: 32rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-item {
  font-size: 48rpx;
  padding: 16rpx;
  text-align: center;
  width: 80rpx;
  height: 80rpx;
  line-height: 48rpx;
}

.input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: flex-end;
  padding: 16rpx 24rpx;
  gap: 16rpx;
  min-height: 104rpx;
  background: #fff;
  border-top: 1rpx solid #e4e7ed;
  /* 确保在键盘弹起时的兼容性 */
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-bar.input-focused {
  border-top-color: rgba(41, 121, 255, 0.2);
  background: #fafbfc;
}

.voice-btn {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 36rpx;
  background: #f5f5f5;
  transition: all 0.2s ease;
}

.voice-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

.input-wrapper {
  flex: 1;
  position: relative;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  min-height: 72rpx;
  max-height: 240rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.input-wrapper.has-text {
  background: #f0f0f0;
}

.input-wrapper:focus-within {
  background: #f0f0f0;
  border-color: rgba(41, 121, 255, 0.3);
  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.1);
}

/* 原生textarea样式 */
.input-textarea {
  width: 100%;
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  min-height: 40rpx;
  max-height: 200rpx;
  padding: 0;
  box-sizing: border-box;
  word-break: break-all;
  white-space: pre-wrap;
}

.input-textarea::placeholder {
  color: #999;
  font-size: 32rpx;
}

/* 字数统计 */
.char-count {
  position: absolute;
  bottom: 8rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
}

.char-count .over-limit {
  color: #ff4757;
  font-weight: 500;
}

/* 防止输入时的布局跳动 */
.input-wrapper {
  will-change: height;
  contain: layout style;
}

.voice-record-btn {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.voice-record-btn.record-ready:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

.voice-record-btn.recording {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  border-color: #ff4757;
  transform: scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}

.record-icon {
  margin-right: 8rpx;
}

.record-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.recording .record-text {
  color: #fff;
}

/* 录音波形动画 */
.record-wave {
  position: absolute;
  right: 16rpx;
  display: flex;
  gap: 4rpx;
  align-items: center;
}

.wave-dot {
  width: 6rpx;
  height: 6rpx;
  background: #fff;
  border-radius: 50%;
  animation: wave 1.4s ease-in-out infinite both;
}

.wave-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.wave-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 36rpx;
  background: #f5f5f5;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

.send-btn {
  width: 72rpx;
  height: 72rpx;
  background: #2979ff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  transform-origin: center;
}

.send-btn.can-send {
  background: #2979ff;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.3);
}

.send-btn.can-send:active {
  transform: scale(0.95);
  background: #1e6bd8;
}

.send-btn.sending {
  background: #999;
  pointer-events: none;
}

.send-btn:not(.can-send) {
  background: #ccc;
  pointer-events: none;
}
</style>
