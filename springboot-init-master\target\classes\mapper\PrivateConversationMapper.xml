<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PrivateConversationMapper">

    <resultMap id="ConversationVOResultMap" type="com.yupi.springbootinit.model.vo.ConversationVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="otherUserId" column="other_user_id" jdbcType="BIGINT"/>
        <result property="otherUserNickname" column="other_user_nickname" jdbcType="VARCHAR"/>
        <result property="otherUserAvatar" column="other_user_avatar" jdbcType="VARCHAR"/>
        <result property="lastMessageId" column="last_message_id" jdbcType="BIGINT"/>
        <result property="lastMessageContent" column="last_message_content" jdbcType="VARCHAR"/>
        <result property="lastMessageTime" column="last_message_time" jdbcType="TIMESTAMP"/>
        <result property="unreadCount" column="unread_count" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 查询两个用户之间的会话 -->
    <select id="selectByUserIds" resultType="com.yupi.springbootinit.model.entity.PrivateConversation">
        SELECT * FROM private_conversations 
        WHERE user1_id = #{user1Id} AND user2_id = #{user2Id} AND is_delete = 0
        LIMIT 1
    </select>

    <!-- 查询用户的会话列表 -->
    <select id="selectConversationList" resultMap="ConversationVOResultMap">
        SELECT 
            pc.id,
            CASE 
                WHEN pc.user1_id = #{userId} THEN pc.user2_id 
                ELSE pc.user1_id 
            END as other_user_id,
            CASE 
                WHEN pc.user1_id = #{userId} THEN u2.nickname 
                ELSE u1.nickname 
            END as other_user_nickname,
            CASE 
                WHEN pc.user1_id = #{userId} THEN u2.avatar 
                ELSE u1.avatar 
            END as other_user_avatar,
            pc.last_message_id,
            pm.content as last_message_content,
            pc.last_message_time,
            CASE 
                WHEN pc.user1_id = #{userId} THEN pc.user1_unread_count 
                ELSE pc.user2_unread_count 
            END as unread_count,
            pc.create_time
        FROM private_conversations pc
        LEFT JOIN ba_user u1 ON pc.user1_id = u1.id
        LEFT JOIN ba_user u2 ON pc.user2_id = u2.id
        LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0
        WHERE (pc.user1_id = #{userId} OR pc.user2_id = #{userId}) AND pc.is_delete = 0
        ORDER BY pc.last_message_time DESC
    </select>

    <!-- 更新会话的最后消息信息 -->
    <update id="updateLastMessage">
        UPDATE private_conversations 
        SET last_message_id = #{lastMessageId}, last_message_time = NOW()
        WHERE id = #{conversationId}
    </update>

    <!-- 增加用户1的未读消息数 -->
    <update id="incrementUser1UnreadCount">
        UPDATE private_conversations 
        SET user1_unread_count = user1_unread_count + #{increment}
        WHERE id = #{conversationId}
    </update>

    <!-- 增加用户2的未读消息数 -->
    <update id="incrementUser2UnreadCount">
        UPDATE private_conversations 
        SET user2_unread_count = user2_unread_count + #{increment}
        WHERE id = #{conversationId}
    </update>

    <!-- 清零用户1的未读消息数 -->
    <update id="clearUser1UnreadCount">
        UPDATE private_conversations 
        SET user1_unread_count = 0
        WHERE id = #{conversationId}
    </update>

    <!-- 清零用户2的未读消息数 -->
    <update id="clearUser2UnreadCount">
        UPDATE private_conversations 
        SET user2_unread_count = 0
        WHERE id = #{conversationId}
    </update>

    <!-- 统计用户的未读会话数 -->
    <select id="countUnreadConversations" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM private_conversations
        WHERE ((user1_id = #{userId} AND user1_unread_count > 0)
               OR (user2_id = #{userId} AND user2_unread_count > 0))
        AND is_delete = 0
    </select>

    <!-- 清空会话的最后消息信息 -->
    <update id="clearLastMessage">
        UPDATE private_conversations
        SET last_message_id = NULL,
            last_message_time = NULL,
            user1_unread_count = 0,
            user2_unread_count = 0
        WHERE id = #{conversationId}
    </update>

</mapper>
