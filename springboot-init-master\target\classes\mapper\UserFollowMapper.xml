<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.UserFollowMapper">

    <resultMap id="UserFollowVOResultMap" type="com.yupi.springbootinit.model.vo.UserFollowVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="followTime" column="follow_time" jdbcType="TIMESTAMP"/>
        <result property="isMutualFollow" column="is_mutual_follow" jdbcType="BOOLEAN"/>
        <result property="followingCount" column="following_count" jdbcType="INTEGER"/>
        <result property="followerCount" column="follower_count" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 查询用户是否已关注 -->
    <select id="selectByFollowerAndFollowing" resultType="com.yupi.springbootinit.model.entity.UserFollow">
        SELECT * FROM user_follows 
        WHERE follower_id = #{followerId} AND following_id = #{followingId}
        LIMIT 1
    </select>

    <!-- 查询用户的关注列表 -->
    <select id="selectFollowingList" resultMap="UserFollowVOResultMap">
        SELECT 
            uf.id,
            uf.following_id as user_id,
            u.nickname,
            u.avatar,
            u.level,
            uf.create_time as follow_time,
            CASE WHEN mf.id IS NOT NULL THEN 1 ELSE 0 END as is_mutual_follow,
            COALESCE(us.following_count, 0) as following_count,
            COALESCE(us.follower_count, 0) as follower_count
        FROM user_follows uf
        LEFT JOIN ba_user u ON uf.following_id = u.id
        LEFT JOIN user_stats us ON uf.following_id = us.user_id
        LEFT JOIN user_follows mf ON uf.following_id = mf.follower_id AND mf.following_id = #{userId} AND mf.is_delete = 0
        WHERE uf.follower_id = #{userId} AND uf.is_delete = 0
        ORDER BY uf.create_time DESC
    </select>

    <!-- 查询用户的粉丝列表 -->
    <select id="selectFollowerList" resultMap="UserFollowVOResultMap">
        SELECT 
            uf.id,
            uf.follower_id as user_id,
            u.nickname,
            u.avatar,
            u.level,
            uf.create_time as follow_time,
            CASE WHEN mf.id IS NOT NULL THEN 1 ELSE 0 END as is_mutual_follow,
            COALESCE(us.following_count, 0) as following_count,
            COALESCE(us.follower_count, 0) as follower_count
        FROM user_follows uf
        LEFT JOIN ba_user u ON uf.follower_id = u.id
        LEFT JOIN user_stats us ON uf.follower_id = us.user_id
        LEFT JOIN user_follows mf ON uf.follower_id = mf.following_id AND mf.follower_id = #{userId} AND mf.is_delete = 0
        WHERE uf.following_id = #{userId} AND uf.is_delete = 0
        ORDER BY uf.create_time DESC
    </select>

    <!-- 查询互相关注的用户列表 -->
    <select id="selectMutualFollowList" resultMap="UserFollowVOResultMap">
        SELECT 
            uf1.id,
            uf1.following_id as user_id,
            u.nickname,
            u.avatar,
            u.level,
            uf1.create_time as follow_time,
            1 as is_mutual_follow,
            COALESCE(us.following_count, 0) as following_count,
            COALESCE(us.follower_count, 0) as follower_count
        FROM user_follows uf1
        INNER JOIN user_follows uf2 ON uf1.following_id = uf2.follower_id AND uf1.follower_id = uf2.following_id
        LEFT JOIN ba_user u ON uf1.following_id = u.id
        LEFT JOIN user_stats us ON uf1.following_id = us.user_id
        WHERE uf1.follower_id = #{userId} AND uf1.is_delete = 0 AND uf2.is_delete = 0
        ORDER BY uf1.create_time DESC
    </select>

    <!-- 批量查询关注状态 -->
    <select id="selectBatchFollowStatus" resultType="com.yupi.springbootinit.model.entity.UserFollow">
        SELECT * FROM user_follows 
        WHERE follower_id = #{followerId} 
        AND following_id IN
        <foreach collection="followingIds" item="followingId" open="(" separator="," close=")">
            #{followingId}
        </foreach>
        AND is_delete = 0
    </select>

    <!-- 统计用户关注数 -->
    <select id="countFollowing" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM user_follows 
        WHERE follower_id = #{userId} AND is_delete = 0
    </select>

    <!-- 统计用户粉丝数 -->
    <select id="countFollower" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM user_follows 
        WHERE following_id = #{userId} AND is_delete = 0
    </select>

    <!-- 查询共同关注的用户 -->
    <select id="selectCommonFollowing" resultMap="UserFollowVOResultMap">
        SELECT 
            uf1.id,
            uf1.following_id as user_id,
            u.nickname,
            u.avatar,
            u.level,
            uf1.create_time as follow_time,
            CASE WHEN mf.id IS NOT NULL THEN 1 ELSE 0 END as is_mutual_follow,
            COALESCE(us.following_count, 0) as following_count,
            COALESCE(us.follower_count, 0) as follower_count
        FROM user_follows uf1
        INNER JOIN user_follows uf2 ON uf1.following_id = uf2.following_id
        LEFT JOIN ba_user u ON uf1.following_id = u.id
        LEFT JOIN user_stats us ON uf1.following_id = us.user_id
        LEFT JOIN user_follows mf ON uf1.following_id = mf.follower_id AND mf.following_id = #{userId1} AND mf.is_delete = 0
        WHERE uf1.follower_id = #{userId1} AND uf2.follower_id = #{userId2}
        AND uf1.is_delete = 0 AND uf2.is_delete = 0
        ORDER BY uf1.create_time DESC
    </select>

</mapper>
