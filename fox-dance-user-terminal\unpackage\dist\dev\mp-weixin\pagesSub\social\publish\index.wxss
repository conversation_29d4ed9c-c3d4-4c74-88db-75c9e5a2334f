@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.publish-container.data-v-bb7c3636 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-bb7c3636 {
  padding: 20px 16px;
  padding-top: calc(20px + 25px);
  /* 状态栏高度 */
  padding-bottom: calc(20px + env(safe-area-inset-bottom));
  /* 安全区域 */
  width: auto;
}
.user-section.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.username.data-v-bb7c3636 {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
/* 标题输入区域 */
.title-section.data-v-bb7c3636 {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
}
.title-input.data-v-bb7c3636 {
  width: 100%;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
  line-height: 1.4;
}
.title-input.data-v-bb7c3636::-webkit-input-placeholder {
  color: #c0c4cc;
  font-weight: 400;
}
.title-input.data-v-bb7c3636::placeholder {
  color: #c0c4cc;
  font-weight: 400;
}
.title-char-count.data-v-bb7c3636 {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
.text-section.data-v-bb7c3636 {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
}
.content-input.data-v-bb7c3636 {
  width: 100%;
  min-height: 120px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
}
.char-count.data-v-bb7c3636 {
  position: absolute;
  bottom: 12px;
  right: 16px;
  font-size: 12px;
  color: #999;
}
.image-section.data-v-bb7c3636 {
  margin-bottom: 16px;
}
.image-grid.data-v-bb7c3636 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.image-item.data-v-bb7c3636 {
  position: relative;
  width: calc(33.33% - 6px);
  height: 100px;
}
.uploaded-image.data-v-bb7c3636 {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.delete-btn.data-v-bb7c3636 {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-image-btn.data-v-bb7c3636 {
  width: calc(33.33% - 6px);
  height: 100px;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.add-text.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
.options-section.data-v-bb7c3636 {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
}
.option-item.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.option-item.data-v-bb7c3636:last-child {
  border-bottom: none;
}
.option-left.data-v-bb7c3636 {
  display: flex;
  align-items: center;
}
.option-text.data-v-bb7c3636 {
  margin-left: 12px;
  font-size: 15px;
  color: #333;
}
.option-right.data-v-bb7c3636 {
  display: flex;
  align-items: center;
}
.selected-topics.data-v-bb7c3636,
.selected-location.data-v-bb7c3636,
.visibility-text.data-v-bb7c3636 {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}
/* 位置选择相关样式 */
.location-selected.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 200px;
}
.location-info-inline.data-v-bb7c3636 {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
}
.selected-location.data-v-bb7c3636 {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.selected-address.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
.tips-section.data-v-bb7c3636 {
  padding: 16px;
}
.tips-text.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}
/* 发布按钮区域 */
.publish-section.data-v-bb7c3636 {
  padding: 24px 16px;
  display: flex;
  justify-content: center;
}
.publish-btn.data-v-bb7c3636 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  padding: 14px 48px;
  border-radius: 28px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  min-width: 120px;
}
.publish-btn.disabled.data-v-bb7c3636 {
  background: #e4e7ed;
  color: #c0c4cc;
  box-shadow: none;
}
.publish-btn.data-v-bb7c3636:not(.disabled):active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.4);
}
.topic-modal.data-v-bb7c3636,
.location-modal.data-v-bb7c3636 {
  background: #fff;
  border-radius: 20px 20px 0 0;
  max-height: 60vh;
}
.modal-header.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.modal-title.data-v-bb7c3636 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.topic-search.data-v-bb7c3636 {
  padding: 16px 20px;
}
.topic-list.data-v-bb7c3636,
.location-list.data-v-bb7c3636 {
  max-height: 300px;
  overflow-y: auto;
}
.topic-option.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}
.topic-option.selected.data-v-bb7c3636 {
  background: #f0f8ff;
}
.topic-name.data-v-bb7c3636 {
  font-size: 15px;
  color: #333;
}
.topic-count.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
}
.location-option.data-v-bb7c3636 {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
}
.location-info.data-v-bb7c3636 {
  margin-left: 12px;
  flex: 1;
}
.location-name.data-v-bb7c3636 {
  font-size: 15px;
  color: #333;
  display: block;
}
.location-address.data-v-bb7c3636 {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}

