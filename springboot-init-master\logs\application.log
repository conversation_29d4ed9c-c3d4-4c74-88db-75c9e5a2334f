2025-07-30 10:07:33.269 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17 on 小伍 with PID 23240 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:07:33.279 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "dev"
2025-07-30 10:07:33.337 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 10:07:33.338 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 10:07:34.414 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-30 10:07:34.432 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.432 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.437 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.438 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.438 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:07:35.442 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-07-30 10:07:35.454 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-30 10:07:35.455 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:07:35.455 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:07:35.563 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:07:35.563 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2225 ms
2025-07-30 10:07:36.081 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:07:36.535 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:07:36.632 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:07:36.638 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:07:36.692 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:07:36.708 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:07:37.091 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:07:37.898 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:07:37.941 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:07:38.036 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:07:38.151 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:07:38.288 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:07:38.404 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-30 10:07:38.425 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api'
2025-07-30 10:07:38.426 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:07:38.429 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:07:38.480 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:07:38.648 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:07:38.764 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:07:38.845 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:07:38.847 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:07:38.849 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:07:38.851 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:07:38.852 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:07:39.080 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:07:39.088 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 6.244 seconds (JVM running for 6.781)
2025-07-30 10:11:20.969 [http-nio-0.0.0.0-8102-exec-4] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:11:20.970 [http-nio-0.0.0.0-8102-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 10:11:20.971 [http-nio-0.0.0.0-8102-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-30 10:16:09.278 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 14140 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:16:09.281 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-30 10:16:09.333 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 10:16:09.333 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 10:16:10.480 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-30 10:16:10.499 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.501 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.502 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.503 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:16:10.504 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:16:11.742 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-30 10:16:11.755 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:16:11.755 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:16:11.756 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:16:11.889 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:16:11.889 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2555 ms
2025-07-30 10:16:12.239 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@58e6274c'
2025-07-30 10:16:12.438 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-30 10:16:12.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-30 10:16:12.506 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-30 10:16:12.532 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-30 10:16:12.553 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-30 10:16:12.566 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:16:12.570 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:16:12.577 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-30 10:16:12.578 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 10:16:12.580 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 10:16:12.580 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 10:16:12.580 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-30 10:16:12.601 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-30 10:16:12.623 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-30 10:16:12.641 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-30 10:16:12.664 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-30 10:16:12.679 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-30 10:16:12.695 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-30 10:16:12.716 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-30 10:16:12.736 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-30 10:16:12.756 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-30 10:16:12.779 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-30 10:16:12.801 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-30 10:16:12.817 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-30 10:16:12.830 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-30 10:16:12.840 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-30 10:16:12.856 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-30 10:16:12.872 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-30 10:16:12.888 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-30 10:16:12.904 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-30 10:16:13.091 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:16:13.221 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:16:13.229 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:16:13.308 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:16:13.334 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:16:13.835 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:16:14.601 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:16:14.641 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:16:14.767 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:16:14.910 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:16:15.086 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:16:15.259 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:16:15.281 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-30 10:16:15.282 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:16:15.286 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:16:15.354 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:16:15.511 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:16:15.609 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:16:15.680 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:16:15.681 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:16:15.682 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:16:15.683 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:16:15.684 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:16:15.863 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:16:15.872 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 7.045 seconds (JVM running for 8.203)
2025-07-30 10:19:11.026 [http-nio-0.0.0.0-8101-exec-2] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:19:11.026 [http-nio-0.0.0.0-8101-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 10:19:11.028 [http-nio-0.0.0.0-8101-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 10:19:11.146 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: fcddacf3-5ddc-7895-6782-c5abf0d0338c
2025-07-30 10:19:11.178 [http-nio-0.0.0.0-8101-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 10:19:11.791 [http-nio-0.0.0.0-8101-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 10:19:11.799 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:19:11.823 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:19:11.890 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:19:11.896 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:19:22.119 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"message","data":{"content":"测试消息 - 上午10:19:22","timestamp":1753841962107}}
2025-07-30 10:19:22.141 [http-nio-0.0.0.0-8101-exec-3] ERROR c.y.s.websocket.ChatWebSocketHandler - 处理WebSocket消息失败
java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map.get(Object)" is null
	at com.yupi.springbootinit.websocket.ChatWebSocketHandler.handleChatMessage(ChatWebSocketHandler.java:170)
	at com.yupi.springbootinit.websocket.ChatWebSocketHandler.handleMessage(ChatWebSocketHandler.java:105)
	at org.springframework.web.socket.handler.WebSocketHandlerDecorator.handleMessage(WebSocketHandlerDecorator.java:75)
	at org.springframework.web.socket.handler.LoggingWebSocketHandlerDecorator.handleMessage(LoggingWebSocketHandlerDecorator.java:56)
	at org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator.handleMessage(ExceptionWebSocketHandlerDecorator.java:58)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.handleTextMessage(StandardWebSocketHandlerAdapter.java:114)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter.access$000(StandardWebSocketHandlerAdapter.java:43)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:85)
	at org.springframework.web.socket.adapter.standard.StandardWebSocketHandlerAdapter$3.onMessage(StandardWebSocketHandlerAdapter.java:82)
	at org.apache.tomcat.websocket.WsFrameBase.sendMessageText(WsFrameBase.java:415)
	at org.apache.tomcat.websocket.server.WsFrameServer.sendMessageText(WsFrameServer.java:129)
	at org.apache.tomcat.websocket.WsFrameBase.processDataText(WsFrameBase.java:515)
	at org.apache.tomcat.websocket.WsFrameBase.processData(WsFrameBase.java:301)
	at org.apache.tomcat.websocket.WsFrameBase.processInputBuffer(WsFrameBase.java:133)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:85)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:183)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:162)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:157)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:59)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-30 10:20:09.128 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:20:32.309 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: a1bfdf6b-9b9a-4706-b15f-0c52592a9255, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:20:32.365 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:20:32.366 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:20:32.414 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:20:32.424 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: a1bfdf6b-9b9a-4706-b15f-0c52592a9255, cost: 126ms
2025-07-30 10:20:32.519 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: d2a0e27f-7225-485d-90e0-a37bd4458aef, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:20:32.522 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:20:32.628 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:20:32.629 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:20:32.675 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:20:32.679 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:20:32.679 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:20:32.731 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.732 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.733 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.733 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.734 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.734 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.735 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.735 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.736 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.736 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.736 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.736 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.737 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.737 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.738 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.738 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.738 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.739 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.739 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:20:32.739 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:20:32.740 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:20:32.740 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: d2a0e27f-7225-485d-90e0-a37bd4458aef, cost: 227ms
2025-07-30 10:20:34.044 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 9f97796f-332d-4ca1-ac7a-5d4a0c83096a, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@6fef49a5]
2025-07-30 10:20:34.061 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:20:34.112 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:20:34.113 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:20:34.156 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:20:34.157 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:20:34.158 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:20:34.204 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:20:34.204 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:20:34.205 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:20:34.205 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:20:34.205 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 9f97796f-332d-4ca1-ac7a-5d4a0c83096a, cost: 162ms
2025-07-30 10:20:35.934 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: fe6efc4a-d682-4f31-96f0-93f2ea267e85, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@6fef49a5]
2025-07-30 10:20:36.000 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-30 10:20:36.001 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-30 10:20:36.046 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-30 10:20:36.047 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-30 10:20:36.048 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-30 10:20:36.075 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: 0bb9aebf-dd8e-20e1-97ff-1979ba562fe1
2025-07-30 10:20:36.097 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 7
2025-07-30 10:20:36.098 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.098 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.116 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.116 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.142 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.143 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.143 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.157 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.157 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:20:36.158 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:20:36.159 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:20:36.187 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.187 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.187 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.231 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.232 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.233 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.277 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.277 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.278 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.321 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.321 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.321 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.366 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.367 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:36.368 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:36.412 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:36.414 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 7
2025-07-30 10:20:36.414 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: fe6efc4a-d682-4f31-96f0-93f2ea267e85, cost: 479ms
2025-07-30 10:20:36.438 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: bea2e28b-3a9e-4b1c-a9df-99cf0e67b9c5, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@6fef49a5]
2025-07-30 10:20:36.438 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: bea2e28b-3a9e-4b1c-a9df-99cf0e67b9c5, cost: 0ms
2025-07-30 10:20:42.186 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"typing","data":{"userId":24840,"chatId":"18","isTyping":true}}
2025-07-30 10:20:43.993 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"typing","data":{"userId":24840,"chatId":"18","isTyping":false}}
2025-07-30 10:20:44.002 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: c6071a60-3c3f-4d5f-b438-b9c82a54bfb5, path: /api/messages/send, ip: 0:0:0:0:0:0:0:1, params: [com.yupi.springbootinit.model.dto.message.MessageSendRequest@24eff163, org.apache.catalina.connector.RequestFacade@6fef49a5]
2025-07-30 10:20:44.096 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:44.097 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 18(Long)
2025-07-30 10:20:44.140 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:44.142 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectByUserIds - ==>  Preparing: SELECT * FROM private_conversations WHERE user1_id = ? AND user2_id = ? AND is_delete = 0 LIMIT 1
2025-07-30 10:20:44.142 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectByUserIds - ==> Parameters: 18(Long), 24840(Long)
2025-07-30 10:20:44.186 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectByUserIds - <==      Total: 1
2025-07-30 10:20:44.186 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 找到已存在会话 - userId1: 24840, userId2: 18, conversationId: 1
2025-07-30 10:20:44.190 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.MessageMapper.insert - ==>  Preparing: INSERT INTO private_messages ( conversation_id, sender_id, receiver_id, message_type, content, is_read, create_time, is_delete ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-30 10:20:44.191 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.MessageMapper.insert - ==> Parameters: 1(Long), 24840(Long), 18(Long), 1(Integer), 1(String), 0(Integer), 2025-07-30 10:20:44.186(Timestamp), 0(Integer)
2025-07-30 10:20:44.280 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.MessageMapper.insert - <==    Updates: 1
2025-07-30 10:20:44.283 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.updateLastMessage - ==>  Preparing: UPDATE private_conversations SET last_message_id = ?, last_message_time = NOW() WHERE id = ?
2025-07-30 10:20:44.283 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.updateLastMessage - ==> Parameters: 11(Long), 1(Long)
2025-07-30 10:20:44.376 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.updateLastMessage - <==    Updates: 1
2025-07-30 10:20:44.376 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectById - ==>  Preparing: SELECT id,user1_id,user2_id,last_message_id,last_message_time,user1_unread_count,user2_unread_count,create_time,is_delete FROM private_conversations WHERE id=? AND is_delete=0
2025-07-30 10:20:44.376 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectById - ==> Parameters: 1(Long)
2025-07-30 10:20:44.421 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectById - <==      Total: 1
2025-07-30 10:20:44.422 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.incrementUser1UnreadCount - ==>  Preparing: UPDATE private_conversations SET user1_unread_count = user1_unread_count + ? WHERE id = ?
2025-07-30 10:20:44.422 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.incrementUser1UnreadCount - ==> Parameters: 1(Integer), 1(Long)
2025-07-30 10:20:44.511 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.incrementUser1UnreadCount - <==    Updates: 1
2025-07-30 10:20:44.511 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 发送消息成功 - messageId: 11, senderId: 24840, receiverId: 18
2025-07-30 10:20:44.511 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:20:44.511 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:20:44.555 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:20:44.648 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 发送消息成功 - senderId: 24840, receiverId: 18, messageId: 11
2025-07-30 10:20:44.648 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: c6071a60-3c3f-4d5f-b438-b9c82a54bfb5, cost: 647ms
2025-07-30 10:20:44.660 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"message","data":{"id":11,"senderId":24840,"receiverId":"18","messageType":1,"content":"1","createTime":"2025-07-30T02:20:44.659Z"}}
2025-07-30 10:20:44.661 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 在房间 18 发送消息
2025-07-30 10:21:06.092 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842066089}
2025-07-30 10:21:36.090 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842096088}
2025-07-30 10:22:06.088 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842126086}
2025-07-30 10:22:36.080 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842156079}
2025-07-30 10:23:06.082 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842186082}
2025-07-30 10:23:36.080 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842216079}
2025-07-30 10:24:06.080 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842246080}
2025-07-30 10:24:36.097 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842276094}
2025-07-30 10:25:06.092 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842306089}
2025-07-30 10:25:36.093 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842336088}
2025-07-30 10:26:06.083 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753842366080}
2025-07-30 10:26:11.966 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:38:22.784 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: dce7dd9b-c597-4d6e-a66c-16c602282e53, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:38:22.826 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:38:22.826 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:38:22.872 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:38:22.873 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: dce7dd9b-c597-4d6e-a66c-16c602282e53, cost: 89ms
2025-07-30 10:38:22.909 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: f292a164-0cf2-4405-b0a3-18b1a06ec7aa, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:22.909 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:22.914 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:38:22.914 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:38:22.958 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:22.958 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:22.959 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:38:23.008 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.008 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.009 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.011 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.011 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.013 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.013 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.013 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.013 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.014 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.014 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.014 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:23.014 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:23.014 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:38:23.015 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: f292a164-0cf2-4405-b0a3-18b1a06ec7aa, cost: 106ms
2025-07-30 10:38:41.859 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 891a3c7d-1136-41e7-8748-6d449c1d243d, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[找搭子], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:41.860 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:41.914 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:41.915 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 找搭子(String)
2025-07-30 10:38:41.958 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:41.959 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND ( p.tags LIKE CONCAT('%', ?, '%') ) ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:41.960 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 找搭子(String), 10(Long)
2025-07-30 10:38:42.004 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.004 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.004 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.004 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.005 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 2
2025-07-30 10:38:42.005 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 891a3c7d-1136-41e7-8748-6d449c1d243d, cost: 145ms
2025-07-30 10:38:42.742 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 55976f1a-c559-417d-b854-cf28de05fd5e, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:42.742 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:42.792 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:38:42.793 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:38:42.836 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:42.836 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:42.837 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:38:42.881 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.882 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.882 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.882 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.882 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.882 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.883 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.884 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:42.885 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:42.885 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:38:42.885 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 55976f1a-c559-417d-b854-cf28de05fd5e, cost: 143ms
2025-07-30 10:38:43.846 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 0616d8d2-9103-4918-a091-7c26fd94e19b, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[找搭子], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:43.846 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:43.893 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:43.894 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 找搭子(String)
2025-07-30 10:38:43.938 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:43.939 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND ( p.tags LIKE CONCAT('%', ?, '%') ) ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:43.940 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 找搭子(String), 10(Long)
2025-07-30 10:38:43.984 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:43.984 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:43.984 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:43.984 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:43.984 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 2
2025-07-30 10:38:43.985 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 0616d8d2-9103-4918-a091-7c26fd94e19b, cost: 139ms
2025-07-30 10:38:44.222 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: d2a4fa9c-e3e1-4f71-9143-b33739ae6896, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[意见反馈], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:44.222 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:44.226 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:44.226 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 意见反馈(String)
2025-07-30 10:38:44.271 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:44.272 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: d2a4fa9c-e3e1-4f71-9143-b33739ae6896, cost: 49ms
2025-07-30 10:38:45.171 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 1ad00267-3fd8-424a-bacf-33b7d6cbc11b, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[话题3], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:45.171 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:45.219 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:45.220 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 话题3(String)
2025-07-30 10:38:45.263 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:45.264 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 1ad00267-3fd8-424a-bacf-33b7d6cbc11b, cost: 92ms
2025-07-30 10:38:45.840 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 0f1d38f2-2c4a-43a1-8ac2-ac80d6709d3f, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[话题4], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:45.840 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:45.887 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:45.888 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 话题4(String)
2025-07-30 10:38:45.931 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:45.931 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 0f1d38f2-2c4a-43a1-8ac2-ac80d6709d3f, cost: 91ms
2025-07-30 10:38:47.227 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 754aec12-3fdf-4a91-b57d-eed57d6d7739, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:47.227 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:47.278 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:38:47.279 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:38:47.322 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:47.323 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:47.323 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:38:47.369 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.369 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.370 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:47.371 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:38:47.372 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 754aec12-3fdf-4a91-b57d-eed57d6d7739, cost: 144ms
2025-07-30 10:38:55.863 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 16965b06-f846-4abd-a197-7c33d9b4de28, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=[找搭子], isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:55.864 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:55.908 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND (p.tags LIKE CONCAT('%', ?, '%'))) TOTAL
2025-07-30 10:38:55.909 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null, 找搭子(String)
2025-07-30 10:38:55.950 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:55.950 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND ( p.tags LIKE CONCAT('%', ?, '%') ) ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:55.951 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 找搭子(String), 10(Long)
2025-07-30 10:38:55.992 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:55.992 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:55.993 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:55.993 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:55.993 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 2
2025-07-30 10:38:55.993 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 16965b06-f846-4abd-a197-7c33d9b4de28, cost: 129ms
2025-07-30 10:38:56.636 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: a6cb8213-80b2-4527-9c9b-d0e7834b012b, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:38:56.636 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:38:56.683 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:38:56.683 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:38:56.727 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:38:56.727 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:38:56.727 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:38:56.772 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.772 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.772 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.773 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:38:56.774 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:38:56.775 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:38:56.775 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: a6cb8213-80b2-4527-9c9b-d0e7834b012b, cost: 138ms
2025-07-30 10:39:02.855 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: a92b40ed-2288-42d7-9a87-375e1dfc6ecb, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [4]
2025-07-30 10:39:02.862 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: ee6820d7-f812-4246-9bea-621d53b9302e, path: /api/users/recommend, ip: 0:0:0:0:0:0:0:1, params: [10, org.apache.catalina.connector.RequestFacade@ac3bfd1]
2025-07-30 10:39:02.864 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.RecommendController - 获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-30 10:39:02.864 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 开始获取推荐用户 - currentUserId: 24840, limit: 10
2025-07-30 10:39:02.865 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 0f0aea3c-ddf6-43e8-9317-09894eb05baf, path: /api/post/hot, ip: 0:0:0:0:0:0:0:1, params: [1, 4, 7, ]
2025-07-30 10:39:02.898 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:39:02.898 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 4(Integer)
2025-07-30 10:39:02.904 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:02.904 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 18(Long)
2025-07-30 10:39:02.912 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner, (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) AS hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)) TOTAL
2025-07-30 10:39:02.913 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - ==> Parameters: null, null, null, 7(Integer)
2025-07-30 10:39:02.944 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 4
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: a92b40ed-2288-42d7-9a87-375e1dfc6ecb, cost: 89ms
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:02.945 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 找到推荐用户 - userId: 18, nickname: joker, avatar: /storage/default/20250409/tmp_58f8b9d60b48df63de68e7fb9726aac32dbbf20cc77c6c9254e.jpeg
2025-07-30 10:39:02.953 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:02.954 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 18(Long)
2025-07-30 10:39:02.957 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.P.selectHotPosts_mpCount - <==      Total: 1
2025-07-30 10:39:02.958 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner , (p.like_count * 3 + p.comment_count * 2 + p.share_count * 1) as hot_score FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 AND p.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) ORDER BY hot_score DESC, p.create_time DESC LIMIT ?
2025-07-30 10:39:02.959 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - ==> Parameters: null, null, null, 7(Integer), 4(Long)
2025-07-30 10:39:02.995 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:02.995 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.RecommendServiceImpl - 获取推荐用户完成 - 返回数量: 1
2025-07-30 10:39:02.995 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.RecommendController - 获取推荐用户成功 - 返回数量: 1
2025-07-30 10:39:02.995 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: ee6820d7-f812-4246-9bea-621d53b9302e, cost: 134ms
2025-07-30 10:39:03.001 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:03.001 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:03.002 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:03.003 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.PostMapper.selectHotPosts - <==      Total: 4
2025-07-30 10:39:03.003 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 0f0aea3c-ddf6-43e8-9317-09894eb05baf, cost: 138ms
2025-07-30 10:39:03.020 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 7db3685c-157b-432b-af79-60aca43536f6, path: /api/follow/batch-status, ip: 0:0:0:0:0:0:0:1, params: [[18], org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:39:03.038 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - ==>  Preparing: SELECT id,follower_id,following_id,status,create_time,update_time,is_delete FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id IN (?) AND status = ?)
2025-07-30 10:39:03.038 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - ==> Parameters: 24840(Long), 18(Long), 1(Integer)
2025-07-30 10:39:03.084 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.FollowMapper.selectList - <==      Total: 1
2025-07-30 10:39:03.085 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.FollowController - 批量检查关注状态成功 - currentUserId: 24840, userCount: 1
2025-07-30 10:39:03.085 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 7db3685c-157b-432b-af79-60aca43536f6, cost: 64ms
2025-07-30 10:39:06.392 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 0b171e0c-6b2f-4510-8b0e-e6d41f17e31b, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:39:06.392 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:39:06.437 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:39:06.437 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:39:06.482 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:39:06.482 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:39:06.483 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:39:06.526 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:39:06.526 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:39:06.526 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:39:06.526 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:39:06.527 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 0b171e0c-6b2f-4510-8b0e-e6d41f17e31b, cost: 135ms
2025-07-30 10:39:08.731 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: eab4fa50-fd72-435c-8712-236627d382c7, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:39:08.778 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:08.778 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:08.822 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:08.823 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:08.823 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:08.866 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:08.866 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:39:08.866 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: eab4fa50-fd72-435c-8712-236627d382c7, cost: 135ms
2025-07-30 10:39:08.881 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 9551e53e-222e-48a8-957e-9e1b45e468c0, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=0, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=1)]
2025-07-30 10:39:08.882 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:08.882 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:08.926 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:08.926 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:08.929 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:08.929 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 0(Integer), 24840(Long)
2025-07-30 10:39:08.973 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:08.973 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 9551e53e-222e-48a8-957e-9e1b45e468c0, cost: 92ms
2025-07-30 10:39:08.989 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: d4b8f904-fde7-4d19-989c-aa519b2cb3c8, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=1, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20)]
2025-07-30 10:39:08.990 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:08.990 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:09.034 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:09.035 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:09.039 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:09.040 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long)
2025-07-30 10:39:09.084 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:09.085 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:39:09.086 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long), 20(Long)
2025-07-30 10:39:09.131 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.131 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.131 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.132 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.133 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 13
2025-07-30 10:39:09.134 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: d4b8f904-fde7-4d19-989c-aa519b2cb3c8, cost: 145ms
2025-07-30 10:39:09.183 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 7c83e676-43ce-4efe-9246-533ca148faf0, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:39:09.183 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:39:09.229 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:39:09.229 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:39:09.270 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:39:09.271 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:39:09.271 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:39:09.314 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:39:09.314 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:39:09.314 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:39:09.314 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:39:09.314 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 7c83e676-43ce-4efe-9246-533ca148faf0, cost: 131ms
2025-07-30 10:39:36.583 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: ebf7de9e-2ac0-47b9-8772-886f86addf2a, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:39:36.626 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:36.626 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:36.671 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:36.671 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:36.672 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:36.716 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:36.716 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:39:36.716 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: ebf7de9e-2ac0-47b9-8772-886f86addf2a, cost: 132ms
2025-07-30 10:39:36.728 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 45eaf4c0-2a50-4d11-90c3-0d9b4e1d0f58, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=0, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=1)]
2025-07-30 10:39:36.729 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:36.729 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:36.774 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:36.774 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:36.777 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:36.777 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 0(Integer), 24840(Long)
2025-07-30 10:39:36.820 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:36.821 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 45eaf4c0-2a50-4d11-90c3-0d9b4e1d0f58, cost: 93ms
2025-07-30 10:39:36.844 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: 0e167a6a-43f4-48a7-8d91-948b76986945, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=1, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20)]
2025-07-30 10:39:36.886 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:36.886 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:36.927 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:36.927 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:36.930 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:36.931 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long)
2025-07-30 10:39:36.973 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:36.974 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:39:36.975 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long), 20(Long)
2025-07-30 10:39:37.016 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.016 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.017 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.018 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 13
2025-07-30 10:39:37.019 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: 0e167a6a-43f4-48a7-8d91-948b76986945, cost: 175ms
2025-07-30 10:39:42.358 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 7b734c77-3054-4e37-a064-c77de079b742, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:39:42.400 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:42.401 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:42.444 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:42.444 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:42.445 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:42.486 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:42.487 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:39:42.487 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 7b734c77-3054-4e37-a064-c77de079b742, cost: 128ms
2025-07-30 10:39:45.425 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 9aa4cbb0-6433-4ff3-adc8-8ddfa9c29322, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:39:45.468 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:45.469 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:45.512 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:45.512 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:45.512 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:45.556 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:45.556 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:39:45.556 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 9aa4cbb0-6433-4ff3-adc8-8ddfa9c29322, cost: 130ms
2025-07-30 10:39:45.569 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: b3daa05f-f932-4f50-a30d-826d9f8c114e, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=0, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=1)]
2025-07-30 10:39:45.569 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:45.569 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:45.613 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:45.613 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:45.616 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:45.616 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 0(Integer), 24840(Long)
2025-07-30 10:39:45.660 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:45.660 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: b3daa05f-f932-4f50-a30d-826d9f8c114e, cost: 91ms
2025-07-30 10:39:45.679 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: cc5a80db-4076-4654-9abb-a81b82d0f9bd, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=24840, keyword=null, tags=null, isPublic=1, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=20)]
2025-07-30 10:39:45.679 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:45.680 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:45.724 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:45.724 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.PostController - 当前用户currentUserId:24840
2025-07-30 10:39:45.728 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ?) TOTAL
2025-07-30 10:39:45.728 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long)
2025-07-30 10:39:45.771 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:45.772 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = ? AND p.user_id = ? ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:39:45.772 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 1(Integer), 24840(Long), 20(Long)
2025-07-30 10:39:45.817 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.817 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.817 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.818 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 13
2025-07-30 10:39:45.820 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: cc5a80db-4076-4654-9abb-a81b82d0f9bd, cost: 141ms
2025-07-30 10:39:54.607 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: f88cb1a9-0a62-40f7-91cd-b33374ddbc7f, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:39:54.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:39:54.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:39:54.694 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:39:54.694 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:39:54.694 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:39:54.694 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:39:54.695 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: f88cb1a9-0a62-40f7-91cd-b33374ddbc7f, cost: 87ms
2025-07-30 10:39:54.727 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 330288c9-e564-4e50-8494-de44191f5eed, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:39:54.727 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:39:54.731 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:39:54.731 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:39:54.775 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:39:54.776 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:39:54.776 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:39:54.821 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.822 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:54.823 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:39:54.824 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 330288c9-e564-4e50-8494-de44191f5eed, cost: 96ms
2025-07-30 10:39:56.492 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: f2cf3704-0c0e-4c3e-9a6d-86657795b555, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:39:56.516 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request start，id: 71c4b076-81de-41d1-ad9b-96a775b538fa, path: /api/post/user, ip: 0:0:0:0:0:0:0:1, params: [24840, 1, 20, , 24840]
2025-07-30 10:39:56.532 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:56.532 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:56.559 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:39:56.559 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:56.573 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:56.574 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:39:56.574 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:39:56.603 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:39:56.607 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectUserPosts_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.user_id = ? AND p.is_delete = 0) TOTAL
2025-07-30 10:39:56.607 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectUserPosts_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:39:56.614 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:39:56.615 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:39:56.615 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: f2cf3704-0c0e-4c3e-9a6d-86657795b555, cost: 122ms
2025-07-30 10:39:56.630 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: be938f46-01fe-447c-8ba8-db7562557eb9, path: /api/follow/status/24840, ip: 0:0:0:0:0:0:0:1, params: [24840, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:39:56.631 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id = ? AND status = ?)
2025-07-30 10:39:56.632 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 24840(Long), 1(Integer)
2025-07-30 10:39:56.653 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.P.selectUserPosts_mpCount - <==      Total: 1
2025-07-30 10:39:56.654 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectUserPosts - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.user_id = ? AND p.is_delete = 0 ORDER BY p.create_time DESC LIMIT ?
2025-07-30 10:39:56.654 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectUserPosts - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 20(Long)
2025-07-30 10:39:56.672 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:39:56.674 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id = ? AND status = ?)
2025-07-30 10:39:56.674 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 24840(Long), 1(Integer)
2025-07-30 10:39:56.698 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.699 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.700 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.701 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.701 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.701 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.702 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.702 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:39:56.702 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:39:56.702 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.m.PostMapper.selectUserPosts - <==      Total: 13
2025-07-30 10:39:56.702 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.aop.LogInterceptor - request end, id: 71c4b076-81de-41d1-ad9b-96a775b538fa, cost: 186ms
2025-07-30 10:39:56.716 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:39:56.717 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND status = ?)
2025-07-30 10:39:56.718 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 1(Integer)
2025-07-30 10:39:56.759 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:39:56.760 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (following_id = ? AND status = ?)
2025-07-30 10:39:56.760 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 1(Integer)
2025-07-30 10:39:56.801 [http-nio-0.0.0.0-8101-exec-10] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:39:56.801 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.controller.FollowController - 检查关注状态成功 - currentUserId: 24840, targetUserId: 24840
2025-07-30 10:39:56.801 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: be938f46-01fe-447c-8ba8-db7562557eb9, cost: 171ms
2025-07-30 10:40:42.859 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: 0bc8678e-6380-4eb8-a1e9-ccb879862317, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:40:42.901 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:40:42.901 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:40:42.943 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:40:42.944 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: 0bc8678e-6380-4eb8-a1e9-ccb879862317, cost: 85ms
2025-07-30 10:40:42.976 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 469cc0e9-0fe5-49db-8892-81992f9ebbac, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:40:42.977 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:40:43.022 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:40:43.023 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:40:43.068 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:40:43.069 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:40:43.069 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:40:43.115 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.115 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.115 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.115 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.116 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.116 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.116 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.116 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.116 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.117 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.118 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.118 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:43.118 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:43.118 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:40:43.118 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 469cc0e9-0fe5-49db-8892-81992f9ebbac, cost: 142ms
2025-07-30 10:40:45.383 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: f32a7de3-d0e1-4293-a879-64e0c7477951, path: /api/post/user, ip: 0:0:0:0:0:0:0:1, params: [24840, 1, 20, , 24840]
2025-07-30 10:40:45.385 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: b7b1b8a4-871d-4a0e-9d52-cde9e55d44c5, path: /api/user/profile/24840, ip: 0:0:0:0:0:0:0:1, params: [24840]
2025-07-30 10:40:45.425 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:40:45.425 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:40:45.426 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:40:45.426 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:40:45.465 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:40:45.466 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==>  Preparing: SELECT user_id,following_count,follower_count,post_count,like_received_count,update_time FROM user_stats WHERE user_id=?
2025-07-30 10:40:45.466 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:40:45.469 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:40:45.472 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectUserPosts_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.user_id = ? AND p.is_delete = 0) TOTAL
2025-07-30 10:40:45.473 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectUserPosts_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:40:45.507 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.UserStatsMapper.selectById - <==      Total: 1
2025-07-30 10:40:45.507 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.c.UserProfileController - 获取用户详情成功 - userId: 24840 - social_id:FOX024840
2025-07-30 10:40:45.507 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: b7b1b8a4-871d-4a0e-9d52-cde9e55d44c5, cost: 123ms
2025-07-30 10:40:45.516 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.P.selectUserPosts_mpCount - <==      Total: 1
2025-07-30 10:40:45.517 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectUserPosts - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.user_id = ? AND p.is_delete = 0 ORDER BY p.create_time DESC LIMIT ?
2025-07-30 10:40:45.517 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectUserPosts - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 20(Long)
2025-07-30 10:40:45.522 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: c9cdd404-9882-4fde-96e1-5eb87c26f3ab, path: /api/follow/status/24840, ip: 0:0:0:0:0:0:0:1, params: [24840, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:40:45.523 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id = ? AND status = ?)
2025-07-30 10:40:45.523 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 24840(Long), 1(Integer)
2025-07-30 10:40:45.562 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.562 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.563 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.564 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND following_id = ? AND status = ?)
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 24840(Long), 1(Integer)
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.566 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/7b8fda5aa9254bfea1d064317666bd02.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/b7e2eb258e4144acb23e24eb6c485510.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:40:45.567 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/26/c3738a4e9677427ca542c0270b8e8da0.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:40:45.568 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.PostMapper.selectUserPosts - <==      Total: 13
2025-07-30 10:40:45.568 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: f32a7de3-d0e1-4293-a879-64e0c7477951, cost: 185ms
2025-07-30 10:40:45.606 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:40:45.607 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (follower_id = ? AND status = ?)
2025-07-30 10:40:45.608 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 1(Integer)
2025-07-30 10:40:45.648 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:40:45.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) FROM user_follows WHERE is_delete=0 AND (following_id = ? AND status = ?)
2025-07-30 10:40:45.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - ==> Parameters: 24840(Long), 1(Integer)
2025-07-30 10:40:45.690 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.FollowMapper.selectCount - <==      Total: 1
2025-07-30 10:40:45.690 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.FollowController - 检查关注状态成功 - currentUserId: 24840, targetUserId: 24840
2025-07-30 10:40:45.690 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: c9cdd404-9882-4fde-96e1-5eb87c26f3ab, cost: 167ms
2025-07-30 10:41:33.281 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 70567eb9-ec0a-4588-a75d-988421965c70, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:41:33.325 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:41:33.325 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:41:33.369 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:41:33.370 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 70567eb9-ec0a-4588-a75d-988421965c70, cost: 88ms
2025-07-30 10:41:33.405 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 03fd5279-fcee-41ff-86c2-c96c19aa835d, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:41:33.405 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:41:33.448 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:41:33.448 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:41:33.490 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:41:33.490 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:41:33.491 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:41:33.532 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.533 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.534 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.535 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:41:33.535 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:41:33.535 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:41:33.535 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 03fd5279-fcee-41ff-86c2-c96c19aa835d, cost: 130ms
2025-07-30 10:45:57.051 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request start，id: afa03629-8639-4cba-b45b-34f9f255c036, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:45:57.051 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:45:57.095 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:45:57.095 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:45:57.137 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:45:57.138 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:45:57.138 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:45:57.179 [http-nio-0.0.0.0-8101-exec-1] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:45:57.179 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:45:57.179 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:45:57.179 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:45:57.179 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.aop.LogInterceptor - request end, id: afa03629-8639-4cba-b45b-34f9f255c036, cost: 127ms
2025-07-30 10:45:59.041 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request start，id: a5dc14df-afea-4326-adc9-2b5ebc4aef3c, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:45:59.087 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-30 10:45:59.088 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-30 10:45:59.130 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-30 10:45:59.131 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-30 10:45:59.131 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-30 10:45:59.175 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 8
2025-07-30 10:45:59.176 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.177 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.221 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.222 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.222 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.234 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: 2c33ae31-d135-dd7e-ea39-e6b470a86de6
2025-07-30 10:45:59.264 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.265 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.265 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.280 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.280 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.308 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.308 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.308 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.325 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.325 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:45:59.326 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:45:59.326 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:45:59.351 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.352 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.353 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.395 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.395 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.396 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.438 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.438 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.439 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.483 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.483 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:45:59.484 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:45:59.530 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:45:59.530 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 8
2025-07-30 10:45:59.531 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.aop.LogInterceptor - request end, id: a5dc14df-afea-4326-adc9-2b5ebc4aef3c, cost: 489ms
2025-07-30 10:45:59.553 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 47a4b390-0279-4bce-ab15-904370811a0d, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:45:59.553 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 47a4b390-0279-4bce-ab15-904370811a0d, cost: 0ms
2025-07-30 10:46:20.580 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:46:20.595 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 1875d5e8-fd14-4f07-a419-d64adc5d3673, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:20.595 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:46:20.638 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:46:20.638 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:46:20.680 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:46:20.681 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:46:20.681 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:46:20.723 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:46:20.724 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:46:20.724 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:46:20.724 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:46:20.724 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 1875d5e8-fd14-4f07-a419-d64adc5d3673, cost: 129ms
2025-07-30 10:46:21.787 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: 8735c05d-b177-f18f-42f1-ae76e738addb
2025-07-30 10:46:21.830 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:21.831 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:21.874 [http-nio-0.0.0.0-8101-exec-7] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:21.874 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:46:21.875 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:46:21.875 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:46:22.588 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request start，id: 9d3a112a-c4e7-43e1-b77d-efc89c75323a, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:22.632 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-30 10:46:22.633 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-30 10:46:22.674 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-30 10:46:22.675 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-30 10:46:22.675 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-30 10:46:22.719 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 8
2025-07-30 10:46:22.720 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.720 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.747 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: f7c76778-5b32-ea13-1d73-2e48fa273f0a
2025-07-30 10:46:22.760 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.773 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.773 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.814 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.814 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.814 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.815 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.816 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.856 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.856 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.856 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.858 [http-nio-0.0.0.0-8101-exec-8] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.859 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:46:22.859 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:46:22.860 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:46:22.897 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.898 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.898 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.939 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.940 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.940 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:22.981 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:22.981 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:22.981 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:23.022 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:23.023 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:23.023 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:23.064 [http-nio-0.0.0.0-8101-exec-9] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:23.064 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 8
2025-07-30 10:46:23.064 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.aop.LogInterceptor - request end, id: 9d3a112a-c4e7-43e1-b77d-efc89c75323a, cost: 475ms
2025-07-30 10:46:23.094 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request start，id: 59f6bcfd-f3e1-4e1d-b8cb-e38530811790, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:23.094 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.aop.LogInterceptor - request end, id: 59f6bcfd-f3e1-4e1d-b8cb-e38530811790, cost: 0ms
2025-07-30 10:46:32.228 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:46:32.241 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request start，id: 25e4a3b5-8f49-4b10-9db7-7173dda8b9f1, path: /api/messages/conversations, ip: 0:0:0:0:0:0:0:1, params: [1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:32.241 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 开始获取会话列表 - userId: 24840, current: 1, size: 50
2025-07-30 10:46:32.286 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END AS other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END AS other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END AS other_user_avatar, pc.last_message_id, pm.content AS last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END AS unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0) TOTAL
2025-07-30 10:46:32.287 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long)
2025-07-30 10:46:32.331 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList_mpCount - <==      Total: 1
2025-07-30 10:46:32.332 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==>  Preparing: SELECT pc.id, CASE WHEN pc.user1_id = ? THEN pc.user2_id ELSE pc.user1_id END as other_user_id, CASE WHEN pc.user1_id = ? THEN u2.nickname ELSE u1.nickname END as other_user_nickname, CASE WHEN pc.user1_id = ? THEN u2.avatar ELSE u1.avatar END as other_user_avatar, pc.last_message_id, pm.content as last_message_content, pc.last_message_time, CASE WHEN pc.user1_id = ? THEN pc.user1_unread_count ELSE pc.user2_unread_count END as unread_count, pc.create_time FROM private_conversations pc LEFT JOIN ba_user u1 ON pc.user1_id = u1.id LEFT JOIN ba_user u2 ON pc.user2_id = u2.id LEFT JOIN private_messages pm ON pc.last_message_id = pm.id AND pm.is_delete = 0 WHERE (pc.user1_id = ? OR pc.user2_id = ?) AND pc.is_delete = 0 ORDER BY pc.last_message_time DESC LIMIT ?
2025-07-30 10:46:32.332 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - ==> Parameters: 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 24840(Long), 50(Long)
2025-07-30 10:46:32.376 [http-nio-0.0.0.0-8101-exec-3] DEBUG c.y.s.m.P.selectConversationList - <==      Total: 2
2025-07-30 10:46:32.377 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 数据库查询结果 - userId: 24840, total: 2, records: 2
2025-07-30 10:46:32.377 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.s.impl.MessageServiceImpl - 获取会话列表成功 - userId: 24840, total: 2, current: 2
2025-07-30 10:46:32.377 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.controller.MessageController - 获取会话列表成功 - userId: 24840, count: 2
2025-07-30 10:46:32.377 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.aop.LogInterceptor - request end, id: 25e4a3b5-8f49-4b10-9db7-7173dda8b9f1, cost: 135ms
2025-07-30 10:46:33.381 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: ef213f9d-2384-f7ad-1a70-d5e0bec366a8
2025-07-30 10:46:33.423 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:33.423 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:33.465 [http-nio-0.0.0.0-8101-exec-2] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:33.465 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:46:33.466 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:46:33.466 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:46:33.997 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request start，id: 028f218e-85ef-4a56-a1f0-a62d515c660b, path: /api/messages/conversation/18, ip: 0:0:0:0:0:0:0:1, params: [18, 1, 50, org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:34.041 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.M.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM private_messages WHERE is_delete = 0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)))
2025-07-30 10:46:34.041 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.M.selectPage_mpCount - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long)
2025-07-30 10:46:34.082 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.M.selectPage_mpCount - <==      Total: 1
2025-07-30 10:46:34.082 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.MessageMapper.selectPage - ==>  Preparing: SELECT id,conversation_id,sender_id,receiver_id,message_type,content,media_url,is_read,read_time,create_time,is_delete FROM private_messages WHERE is_delete=0 AND (((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))) ORDER BY create_time DESC LIMIT ?
2025-07-30 10:46:34.082 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.MessageMapper.selectPage - ==> Parameters: 24840(Long), 18(Long), 18(Long), 24840(Long), 50(Long)
2025-07-30 10:46:34.123 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - WebSocket连接建立: 9b65a6a0-2fd2-a4a5-bb25-4577f96893c4
2025-07-30 10:46:34.124 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.m.MessageMapper.selectPage - <==      Total: 8
2025-07-30 10:46:34.125 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.125 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.166 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.166 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.167 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.167 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.167 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.208 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.209 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.210 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.210 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.211 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 连接成功
2025-07-30 10:46:34.222 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"join","chatId":"18","userId":24840}
2025-07-30 10:46:34.223 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 加入聊天房间: 18
2025-07-30 10:46:34.250 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.251 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.251 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.332 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.332 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.333 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.373 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.374 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.374 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.416 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.416 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.416 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.457 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.457 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==>  Preparing: SELECT id,username,nickname,social_id,mobile,avatar,level,is_member,remaining_votes,bio,dance_type FROM ba_user WHERE id=?
2025-07-30 10:46:34.458 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - ==> Parameters: 24840(Long)
2025-07-30 10:46:34.499 [http-nio-0.0.0.0-8101-exec-4] DEBUG c.y.s.mapper.BaUserMapper.selectById - <==      Total: 1
2025-07-30 10:46:34.499 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.controller.MessageController - 获取会话消息成功 - currentUserId: 24840, targetUserId: 18, count: 8
2025-07-30 10:46:34.499 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.aop.LogInterceptor - request end, id: 028f218e-85ef-4a56-a1f0-a62d515c660b, cost: 501ms
2025-07-30 10:46:34.517 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request start，id: 9c28f368-fa2e-4aab-9104-1e584e413dc2, path: /api/messages/read, ip: 0:0:0:0:0:0:0:1, params: [, , org.apache.catalina.connector.RequestFacade@74d298aa]
2025-07-30 10:46:34.540 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.aop.LogInterceptor - request end, id: 9c28f368-fa2e-4aab-9104-1e584e413dc2, cost: 23ms
2025-07-30 10:46:51.791 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843611789}
2025-07-30 10:47:03.386 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843623385}
2025-07-30 10:47:04.224 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843624223}
2025-07-30 10:47:21.797 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843641794}
2025-07-30 10:47:33.393 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843653392}
2025-07-30 10:47:34.236 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843654235}
2025-07-30 10:47:51.799 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843671795}
2025-07-30 10:48:03.399 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843683398}
2025-07-30 10:48:04.229 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843684228}
2025-07-30 10:48:21.796 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843701794}
2025-07-30 10:48:33.394 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843713393}
2025-07-30 10:48:34.225 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843714224}
2025-07-30 10:48:51.800 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843731797}
2025-07-30 10:49:03.386 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843743384}
2025-07-30 10:49:04.228 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843744227}
2025-07-30 10:49:21.801 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843761798}
2025-07-30 10:49:33.393 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843773392}
2025-07-30 10:49:34.224 [http-nio-0.0.0.0-8101-exec-4] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843774223}
2025-07-30 10:49:51.797 [http-nio-0.0.0.0-8101-exec-7] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843791793}
2025-07-30 10:50:03.385 [http-nio-0.0.0.0-8101-exec-8] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843803384}
2025-07-30 10:50:04.231 [http-nio-0.0.0.0-8101-exec-9] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843804230}
2025-07-30 10:50:21.797 [http-nio-0.0.0.0-8101-exec-10] INFO  c.y.s.websocket.ChatWebSocketHandler - 收到WebSocket消息: {"type":"heartbeat","timestamp":1753843821793}
2025-07-30 10:50:30.956 [http-nio-0.0.0.0-8101-exec-1] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:50:30.957 [http-nio-0.0.0.0-8101-exec-2] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:50:30.957 [http-nio-0.0.0.0-8101-exec-3] INFO  c.y.s.websocket.ChatWebSocketHandler - 用户 24840 断开连接: CloseStatus[code=1000, reason=null]
2025-07-30 10:52:56.437 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request start，id: 464e258b-9822-44ff-96b0-043817aa62a7, path: /api/tag/hot, ip: 0:0:0:0:0:0:0:1, params: [10]
2025-07-30 10:52:56.481 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==>  Preparing: SELECT id, name, description, cover_image, color, use_count, is_hot, create_time, update_time, is_delete FROM tags WHERE is_delete = 0 ORDER BY COALESCE(use_count, 0) DESC, create_time DESC LIMIT ?
2025-07-30 10:52:56.481 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - ==> Parameters: 10(Integer)
2025-07-30 10:52:56.525 [http-nio-0.0.0.0-8101-exec-5] DEBUG c.y.s.m.TagMapper.selectTopUsedTags - <==      Total: 7
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 1, name: 找搭子, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 2, name: 意见反馈, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 3, name: 话题3, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 4, name: 话题4, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 5, name: 话题5, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 6, name: 话题6, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.service.impl.TagServiceImpl - 热门标签数据 - id: 7, name: 话题7, coverImage: https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.controller.TagController - 热门标签数据 - [Tag(id=1, name=找搭子, description=来寻找你的跳舞搭子吧~, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/6f28840053fb4f149011a987b57785fc.png, color=#1890ff, useCount=5, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Mon Jul 28 10:42:20 CST 2025, isDelete=0), Tag(id=2, name=意见反馈, description=你对 FOX 有什么好的建议吗？, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d19cda1857b74467b8eb0753f2c7b12e.png, color=#f5222d, useCount=1, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:39:06 CST 2025, isDelete=0), Tag(id=3, name=话题3, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/f4d78085de0045089e2ba92bb59ba8d2.png, color=#52c41a, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=4, name=话题4, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/616f27a233dc4ddd828ff761f8636294.png, color=#722ed1, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=5, name=话题5, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/d055f94455a24eb585b6c7dd41227abc.png, color=#fa8c16, useCount=0, isHot=1, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=6, name=话题6, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#13c2c2, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0), Tag(id=7, name=话题7, description=123, coverImage=https://file.foxdance.com.cn/social/posts/2025/07/25/41820570eb0a4e31b85b60fcf0a32b38.png, color=#eb2f96, useCount=0, isHot=0, createTime=Fri Jul 11 15:58:53 CST 2025, updateTime=Fri Jul 25 23:28:44 CST 2025, isDelete=0)]
2025-07-30 10:52:56.526 [http-nio-0.0.0.0-8101-exec-5] INFO  c.y.s.aop.LogInterceptor - request end, id: 464e258b-9822-44ff-96b0-043817aa62a7, cost: 88ms
2025-07-30 10:52:56.556 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request start，id: 30c8c678-0e9d-419c-8a31-b8af7124f79b, path: /api/post/list, ip: 0:0:0:0:0:0:0:1, params: [PostQueryDTO(id=null, userId=null, keyword=null, tags=null, isPublic=null, status=null, startTime=null, endTime=null, sortField=createTime, sortOrder=desc, centerLatitude=null, centerLongitude=null, radius=null, current=1, pageSize=10)]
2025-07-30 10:52:56.557 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.controller.PostController - 当前用户currentUserId:null
2025-07-30 10:52:56.560 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level, CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END AS is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END AS is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1) TOTAL
2025-07-30 10:52:56.560 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - ==> Parameters: null, null, null
2025-07-30 10:52:56.604 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.P.selectPostPage_mpCount - <==      Total: 1
2025-07-30 10:52:56.605 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==>  Preparing: SELECT p.id, p.user_id, p.title, p.content, p.images, p.cover_image, p.location_name, p.location_latitude, p.location_longitude, p.location_address, p.like_count, p.comment_count, p.share_count, p.view_count, p.is_public, p.status, p.create_time, u.nickname, u.avatar, u.level , CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, CASE WHEN pf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited, CASE WHEN p.user_id = ? THEN 1 ELSE 0 END as is_owner FROM posts p LEFT JOIN ba_user u ON p.user_id = u.id LEFT JOIN post_likes pl ON p.id = pl.post_id AND pl.user_id = ? AND pl.is_delete = 0 LEFT JOIN post_favorites pf ON p.id = pf.post_id AND pf.user_id = ? AND pf.is_delete = 0 WHERE p.is_delete = 0 AND p.status = 1 AND p.is_public = 1 ORDER BY p.create_time desc LIMIT ?
2025-07-30 10:52:56.605 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - ==> Parameters: null, null, null, 10(Long)
2025-07-30 10:52:56.648 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.648 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85", "https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/29/2530c9c8af724be5973816bbd86a541a.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4c3326b96fe84b37bd19e987fb9dcb6d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/4cfdc75946424fe59b816484d290cc1d.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/7528317726f6465f824fa5a8791f76b8.png?imageMogr2/format/webp/quality/85, https://file.foxdance.com.cn/social/posts/2025/07/29/cb088c510dbe42a3a537836516eb7cdb.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/68415fdbcd9046fa8b046d8991bd45ea.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/18dc1eff11984a059a9cd17dbeea5977.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/bdf1e8086e534e60a5b1b399cd937ad2.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/525b0803e7834022a7fac5052d0fd5ec.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/cf2ece5ca73c4a1d9edb2783d75f496c.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/4957af7d142a4ae79820117b6906c504.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/6102f9b96aa344ecb495afafd7cd6d03.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.649 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/79fcee80a2724fce9f0343f196b5c38e.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.getNullableResult(columnName) - 从数据库读取JSON: columnName=images, value=["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"]
2025-07-30 10:52:56.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.config.ListJsonTypeHandler - 🔍 ListJsonTypeHandler.parseJson - JSON反序列化成功: ["https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85"] -> [https://file.foxdance.com.cn/social/posts/2025/07/28/0d6a39de85bf4852a201c0abbb5c3815.png?imageMogr2/format/webp/quality/85]
2025-07-30 10:52:56.650 [http-nio-0.0.0.0-8101-exec-6] DEBUG c.y.s.m.PostMapper.selectPostPage - <==      Total: 10
2025-07-30 10:52:56.650 [http-nio-0.0.0.0-8101-exec-6] INFO  c.y.s.aop.LogInterceptor - request end, id: 30c8c678-0e9d-419c-8a31-b8af7124f79b, cost: 93ms
2025-07-30 10:54:29.740 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 10:54:29.744 [Thread-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-30 10:54:29.998 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 14140 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:54:29.999 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-30 10:54:30.572 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:30.573 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:54:30.779 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-30 10:54:30.780 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:54:30.780 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:54:30.780 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:54:30.813 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:54:30.814 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 808 ms
2025-07-30 10:54:30.895 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5a45dbe6'
2025-07-30 10:54:30.912 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-30 10:54:30.923 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-30 10:54:30.934 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-30 10:54:30.948 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-30 10:54:30.960 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-30 10:54:30.965 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:54:30.967 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:54:30.971 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-30 10:54:30.972 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 10:54:30.973 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 10:54:30.973 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 10:54:30.973 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-30 10:54:31.009 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-30 10:54:31.023 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-30 10:54:31.034 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-30 10:54:31.052 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-30 10:54:31.064 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-30 10:54:31.077 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-30 10:54:31.088 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-30 10:54:31.102 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-30 10:54:31.116 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-30 10:54:31.128 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-30 10:54:31.143 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-30 10:54:31.155 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-30 10:54:31.166 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-30 10:54:31.179 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-30 10:54:31.195 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-30 10:54:31.205 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-30 10:54:31.216 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-30 10:54:31.227 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-30 10:54:31.327 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:54:31.371 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:54:31.376 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:54:31.438 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:54:31.449 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:54:31.672 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:54:32.338 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:54:32.369 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:54:32.437 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:54:32.498 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:54:32.569 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:54:32.646 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:54:32.649 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-30 10:54:32.650 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:54:32.650 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:54:32.670 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:54:32.711 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:54:32.763 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:54:32.808 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:54:32.810 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:54:32.811 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:54:32.812 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:54:32.813 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:54:32.934 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:54:32.935 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 3.009 seconds (JVM running for 2305.266)
2025-07-30 10:54:32.937 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-30 10:54:49.965 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 14140 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:54:49.965 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.278 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.279 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.279 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.279 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.279 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:54:50.279 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:54:50.371 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-30 10:54:50.371 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:54:50.371 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:54:50.372 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:54:50.389 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:54:50.389 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 422 ms
2025-07-30 10:54:50.449 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1b7105b6'
2025-07-30 10:54:50.462 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-30 10:54:50.476 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-30 10:54:50.486 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-30 10:54:50.501 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-30 10:54:50.515 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-30 10:54:50.520 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:54:50.521 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:54:50.525 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-30 10:54:50.526 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 10:54:50.526 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 10:54:50.527 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 10:54:50.527 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-30 10:54:50.541 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-30 10:54:50.552 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-30 10:54:50.561 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-30 10:54:50.575 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-30 10:54:50.585 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-30 10:54:50.597 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-30 10:54:50.613 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-30 10:54:50.626 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-30 10:54:50.643 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-30 10:54:50.658 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-30 10:54:50.674 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-30 10:54:50.688 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-30 10:54:50.701 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-30 10:54:50.715 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-30 10:54:50.729 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-30 10:54:50.742 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-30 10:54:50.755 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-30 10:54:50.775 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-30 10:54:50.917 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:54:50.957 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:54:50.962 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:54:51.029 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:54:51.038 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:54:51.240 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:54:51.844 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:54:51.872 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:54:51.926 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:54:51.971 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:54:52.016 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:54:52.077 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:54:52.080 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-30 10:54:52.081 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:54:52.081 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:54:52.103 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:54:52.132 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:54:52.174 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:54:52.214 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:54:52.215 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:54:52.216 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:54:52.217 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:54:52.220 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:54:52.356 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:54:52.357 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.419 seconds (JVM running for 2324.689)
2025-07-30 10:54:52.358 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-30 10:55:09.505 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17.0.15 on 小伍 with PID 14140 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:55:09.505 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "prod"
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:55:09.902 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:55:09.998 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8101 (http)
2025-07-30 10:55:09.999 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:55:09.999 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:55:09.999 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:55:10.019 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:55:10.019 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 512 ms
2025-07-30 10:55:10.086 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@21942eeb'
2025-07-30 10:55:10.101 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaCardRecordMapper.xml]'
2025-07-30 10:55:10.111 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\BaUserMapper.xml]'
2025-07-30 10:55:10.120 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentLikeMapper.xml]'
2025-07-30 10:55:10.136 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentMapper.xml]'
2025-07-30 10:55:10.147 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\CommentReplyMapper.xml]'
2025-07-30 10:55:10.152 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:55:10.153 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 10:55:10.157 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectList] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectList]
2025-07-30 10:55:10.158 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-30 10:55:10.159 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.updateById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.UpdateById]
2025-07-30 10:55:10.159 [restartedMain] WARN  c.b.m.core.injector.AbstractMethod - [com.yupi.springbootinit.mapper.MetroLineMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 10:55:10.159 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\MetroLineMapper.xml]'
2025-07-30 10:55:10.170 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\NotificationMapper.xml]'
2025-07-30 10:55:10.181 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostFavoriteMapper.xml]'
2025-07-30 10:55:10.192 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostLikeMapper.xml]'
2025-07-30 10:55:10.206 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostMapper.xml]'
2025-07-30 10:55:10.215 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostShareMapper.xml]'
2025-07-30 10:55:10.225 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostStatsMapper.xml]'
2025-07-30 10:55:10.236 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PostTagMapper.xml]'
2025-07-30 10:55:10.249 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateConversationMapper.xml]'
2025-07-30 10:55:10.262 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\PrivateMessageMapper.xml]'
2025-07-30 10:55:10.272 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReplyLikeMapper.xml]'
2025-07-30 10:55:10.291 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\ReportMapper.xml]'
2025-07-30 10:55:10.303 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\SystemConfigMapper.xml]'
2025-07-30 10:55:10.314 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TagMapper.xml]'
2025-07-30 10:55:10.325 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\TopicMapper.xml]'
2025-07-30 10:55:10.335 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserFollowMapper.xml]'
2025-07-30 10:55:10.345 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\UserStatsMapper.xml]'
2025-07-30 10:55:10.353 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteInfoMapper.xml]'
2025-07-30 10:55:10.363 [restartedMain] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes\mapper\VoteRecordMapper.xml]'
2025-07-30 10:55:10.465 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:55:10.504 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:55:10.508 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:55:10.571 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:55:10.581 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:55:10.796 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:55:11.575 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:55:11.616 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:55:11.709 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:55:11.775 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:55:11.832 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:55:11.909 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8101"]
2025-07-30 10:55:11.918 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8101 (http) with context path '/api'
2025-07-30 10:55:11.919 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:55:11.920 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:55:11.938 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:55:11.978 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:55:12.020 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:55:12.055 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:55:12.055 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:55:12.056 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:55:12.057 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:55:12.058 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:55:12.172 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:55:12.173 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 2.701 seconds (JVM running for 2344.505)
2025-07-30 10:55:12.175 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
