2025-07-30 10:07:33.269 [restartedMain] INFO  c.y.springbootinit.MainApplication - Starting MainApplication using Java 17 on 小伍 with PID 23240 (D:\求职之路\Fox\用户端\用户端\springboot-init-master\target\classes started by 16935 in D:\求职之路\Fox\用户端\用户端\springboot-init-master)
2025-07-30 10:07:33.279 [restartedMain] INFO  c.y.springbootinit.MainApplication - The following 1 profile is active: "dev"
2025-07-30 10:07:33.337 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 10:07:33.338 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 10:07:34.414 [restartedMain] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-30 10:07:34.432 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baCardRecordMapper' and 'com.yupi.springbootinit.mapper.BaCardRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.432 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'baUserMapper' and 'com.yupi.springbootinit.mapper.BaUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'captchaMapper' and 'com.yupi.springbootinit.mapper.CaptchaMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentLikeMapper' and 'com.yupi.springbootinit.mapper.CommentLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentMapper' and 'com.yupi.springbootinit.mapper.CommentMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'commentReplyMapper' and 'com.yupi.springbootinit.mapper.CommentReplyMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followMapper' and 'com.yupi.springbootinit.mapper.FollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.433 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'messageMapper' and 'com.yupi.springbootinit.mapper.MessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'metroLineMapper' and 'com.yupi.springbootinit.mapper.MetroLineMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'notificationMapper' and 'com.yupi.springbootinit.mapper.NotificationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postFavoriteMapper' and 'com.yupi.springbootinit.mapper.PostFavoriteMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postLikeMapper' and 'com.yupi.springbootinit.mapper.PostLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postMapper' and 'com.yupi.springbootinit.mapper.PostMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.434 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postShareMapper' and 'com.yupi.springbootinit.mapper.PostShareMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postStatsMapper' and 'com.yupi.springbootinit.mapper.PostStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'postTagMapper' and 'com.yupi.springbootinit.mapper.PostTagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateConversationMapper' and 'com.yupi.springbootinit.mapper.PrivateConversationMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'privateMessageMapper' and 'com.yupi.springbootinit.mapper.PrivateMessageMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'replyLikeMapper' and 'com.yupi.springbootinit.mapper.ReplyLikeMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'reportMapper' and 'com.yupi.springbootinit.mapper.ReportMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.435 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'storeMapper' and 'com.yupi.springbootinit.mapper.StoreMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'systemConfigMapper' and 'com.yupi.springbootinit.mapper.SystemConfigMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'tagMapper' and 'com.yupi.springbootinit.mapper.TagMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'topicMapper' and 'com.yupi.springbootinit.mapper.TopicMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userFollowMapper' and 'com.yupi.springbootinit.mapper.UserFollowMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userMapper' and 'com.yupi.springbootinit.mapper.UserMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.436 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'userStatsMapper' and 'com.yupi.springbootinit.mapper.UserStatsMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.437 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteInfoMapper' and 'com.yupi.springbootinit.mapper.VoteInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.438 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'voteRecordMapper' and 'com.yupi.springbootinit.mapper.VoteRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 10:07:34.438 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yupi.springbootinit.mapper]' package. Please check your configuration.
2025-07-30 10:07:35.442 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-07-30 10:07:35.454 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-30 10:07:35.455 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:07:35.455 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-30 10:07:35.563 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:07:35.563 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2225 ms
2025-07-30 10:07:36.081 [restartedMain] ERROR c.b.m.core.MybatisConfiguration - mapper[com.yupi.springbootinit.mapper.MetroLineMapper.updateVoteCount] is ignored, because it exists, maybe from xml file
2025-07-30 10:07:36.535 [restartedMain] INFO  c.y.s.config.CacheConfig - ObjectMapper配置完成
2025-07-30 10:07:36.632 [restartedMain] INFO  c.y.s.config.CacheConfig - 评论专用Caffeine缓存初始化完成
2025-07-30 10:07:36.638 [restartedMain] INFO  c.y.s.config.CacheConfig - 话题专用Caffeine缓存初始化完成
2025-07-30 10:07:36.692 [restartedMain] INFO  c.y.s.config.HttpClientConfig - 🔧 RestTemplate配置完成 - 超时时间: 5000ms
2025-07-30 10:07:36.708 [restartedMain] INFO  c.y.s.s.i.ThirdPartySensitiveWordServiceImpl - 🔧 第三方敏感词检测服务初始化完成 - API地址: https://v.api.aa1.cn/api/api-mgc/index.php, 超时时间: 5000ms, 启用状态: true
2025-07-30 10:07:37.091 [restartedMain] INFO  c.y.s.utils.IpLocationUtils - IP地理位置数据库加载成功
2025-07-30 10:07:37.898 [restartedMain] INFO  c.y.s.config.CacheConfig - Caffeine本地缓存管理器初始化完成
2025-07-30 10:07:37.941 [restartedMain] INFO  c.y.s.config.WebSocketConfig - 🔌 WebSocket配置完成 - 聊天端点: /ws/chat
2025-07-30 10:07:38.036 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-30 10:07:38.151 [restartedMain] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:07:38.288 [restartedMain] WARN  o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-30 10:07:38.404 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-8102"]
2025-07-30 10:07:38.425 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api'
2025-07-30 10:07:38.426 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-07-30 10:07:38.429 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-07-30 10:07:38.480 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-07-30 10:07:38.648 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: uploadFileUsingPOST_1
2025-07-30 10:07:38.764 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getUnreadCountUsingGET_1
2025-07-30 10:07:38.845 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: favoritePostUsingPOST_1
2025-07-30 10:07:38.847 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: likePostUsingPOST_1
2025-07-30 10:07:38.849 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: sharePostUsingPOST_1
2025-07-30 10:07:38.851 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unfavoritePostUsingPOST_1
2025-07-30 10:07:38.852 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: unlikePostUsingPOST_1
2025-07-30 10:07:39.080 [restartedMain] INFO  o.s.s.a.ScheduledAnnotationBeanPostProcessor - No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
2025-07-30 10:07:39.088 [restartedMain] INFO  c.y.springbootinit.MainApplication - Started MainApplication in 6.244 seconds (JVM running for 6.781)
2025-07-30 10:11:20.969 [http-nio-0.0.0.0-8102-exec-4] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:11:20.970 [http-nio-0.0.0.0-8102-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 10:11:20.971 [http-nio-0.0.0.0-8102-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
