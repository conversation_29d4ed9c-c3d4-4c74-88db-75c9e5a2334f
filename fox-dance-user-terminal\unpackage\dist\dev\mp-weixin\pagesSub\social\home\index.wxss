@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
.home-container.data-v-de45d8c2 {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  margin: 0;
  padding: 0;
}
.header.data-v-de45d8c2 {
  background: #fff;
  border-bottom: 2rpx solid #e4e7ed;
  padding-top: 25px;
  flex-shrink: 0;
}
.header-content.data-v-de45d8c2 {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.logo-text.data-v-de45d8c2 {
  width: 146.08rpx;
  height: 68.75rpx;
  margin-left: -10rpx;
}
.topic-tabs-container.data-v-de45d8c2 {
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
  flex-shrink: 0;
}
/* uview tabs组件样式优化 */
.topic-tabs-container.data-v-de45d8c2  .u-tabs {
  background: #fff;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__item {
  padding: 0 32rpx !important;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__item__text {
  font-size: 28rpx !important;
  font-weight: 500;
}
.topic-tabs-container.data-v-de45d8c2  .u-tabs__wrapper__nav__line {
  border-radius: 6rpx;
}
.post-list.data-v-de45d8c2 {
  flex: 1;
  height: 0;
  /* 重要：让flex子元素正确计算高度 */
  overflow: hidden;
}
/* scroll-view内部内容的样式 */
.post-list.data-v-de45d8c2  .uni-scroll-view {
  height: 100% !important;
  overflow-x: hidden !important;
}
.post-list.data-v-de45d8c2  .uni-scroll-view-content {
  min-height: 100%;
}
.post-grid.data-v-de45d8c2 {
  display: flex;
  flex-wrap: wrap;
  gap: 14rpx;
  padding: 26rpx;
  padding-bottom: 100rpx;
  /* 底部留出更多空间 */
}
.post-card-item.data-v-de45d8c2 {
  width: calc(50% - 8rpx);
  margin-bottom: 16rpx;
}
.empty-state.data-v-de45d8c2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 40rpx;
}
.empty-text.data-v-de45d8c2 {
  font-size: 32rpx;
  color: #999;
  margin: 32rpx 0 16rpx;
}
.empty-desc.data-v-de45d8c2 {
  font-size: 28rpx;
  color: #ccc;
}
.load-more.data-v-de45d8c2 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.load-text.data-v-de45d8c2 {
  margin-left: 16rpx;
  color: #999;
  font-size: 28rpx;
}
.load-text.no-more.data-v-de45d8c2 {
  color: #ccc;
  font-size: 26rpx;
  margin-left: 0;
}
.load-text.error.data-v-de45d8c2 {
  color: #ff6b6b;
  margin-left: 0;
  padding: 16rpx 32rpx;
  border: 2rpx solid #ff6b6b;
  border-radius: 32rpx;
  background: rgba(255, 107, 107, 0.1);
}
.load-text.error.data-v-de45d8c2:active {
  background: rgba(255, 107, 107, 0.2);
}
/* 响应式设计 */
@media screen and (max-width: 375px) {
.post-list.data-v-de45d8c2 {
    padding: 12rpx;
}
.post-grid.data-v-de45d8c2 {
    gap: 12rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(50% - 6rpx);
}
}
@media screen and (min-width: 768px) {
.post-grid.data-v-de45d8c2 {
    gap: 24rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(33.33% - 16rpx);
}
}
@media screen and (min-width: 1024px) {
.post-list.data-v-de45d8c2 {
    padding: 32rpx 64rpx;
}
.post-card-item.data-v-de45d8c2 {
    width: calc(25% - 18rpx);
}
}
.data-v-de45d8c2 .u-tab-item {
  padding: 0 25rpx;
}

