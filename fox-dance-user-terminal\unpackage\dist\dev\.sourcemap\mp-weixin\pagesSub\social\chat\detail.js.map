{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?59f1", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?a62f", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?e65c", "uni-app:///pagesSub/social/chat/detail.vue", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?ef87", "webpack:///D:/求职之路/Fox/用户端/用户端/fox-dance-user-terminal/pagesSub/social/chat/detail.vue?d4d8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "chatId", "chatName", "otherUserAvatar", "isOnline", "messageList", "inputText", "scrollTop", "showExtensions", "showEmojis", "voiceMode", "isRecording", "inputFocused", "isSending", "isTyping", "typingTimer", "currentUser", "avatar", "recorder<PERSON>anager", "voiceRecordPath", "voiceRecordDuration", "recordStartTime", "currentPlayingVoice", "innerAudioContext", "emojiList", "inputCustomStyle", "backgroundColor", "fontSize", "lineHeight", "minHeight", "maxHeight", "padding", "color", "placeholder<PERSON><PERSON><PERSON>", "socketTask", "isConnected", "reconnectTimer", "reconnectCount", "maxReconnectCount", "heartbeatTimer", "onLoad", "console", "decodeURIComponent", "computed", "canSendMessage", "onUnload", "onHide", "onShow", "methods", "connectWebSocket", "url", "protocols", "success", "fail", "uni", "title", "icon", "duration", "simulateWebSocketConnection", "setTimeout", "simulateReceiveMessage", "id", "senderId", "receiverId", "messageType", "content", "createTime", "isRead", "simulateReply", "originalMessage", "testHttpConnection", "method", "disconnectWebSocket", "clearTimeout", "scheduleReconnect", "startHeartbeat", "type", "timestamp", "stopHeartbeat", "clearInterval", "getWebSocketUrl", "process", "systemInfo", "joinChatRoom", "userId", "handleWebSocketMessage", "handleNewMessage", "isMine", "showTime", "status", "isPlaying", "message", "handleTypingStatus", "handleMessageRead", "handleOnlineStatus", "shouldShowTime", "playMessageSound", "loadCurrentUserInfo", "loadMessages", "currentUserId", "current", "size", "result", "messages", "Array", "map", "messageObj", "parseInt", "reverse", "getCurrentUserId", "getMessageTypeString", "markAllMessagesAsRead", "formatMessageTime", "getHours", "toString", "padStart", "getMinutes", "scrollToBottom", "sendMessage", "messageContent", "tempId", "chooseImage", "count", "sizeType", "sourceType", "previewImage", "urls", "startVoiceRecord", "toggleVoiceMode", "initRecorderManager", "startRecord", "scope", "sampleRate", "numberOfChannels", "encodeBitRate", "format", "frameSize", "confirmText", "stopRecord", "cancelRecord", "sendVoiceMessage", "tempFile<PERSON>ath", "tempMessage", "uploadResult", "mediaUrl", "mediaDuration", "mediaSize", "uploadVoiceFile", "filePath", "formData", "driver", "header", "b<PERSON><PERSON><PERSON>", "userid", "resolve", "reject", "playVoice", "msg", "cleanupAudio", "getApiBaseUrl", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleExtensions", "insert<PERSON><PERSON><PERSON>", "onInputFocus", "onInputBlur", "onInputChange", "onLineChange", "onInputConfirm", "sendTypingIndicator", "stopTypingIndicator", "goBack", "makeCall", "phoneNumber", "showMoreActions", "itemList", "viewUserProfile", "clearChatHistory", "confirmColor", "clearChatHistoryFromServer", "reportUser", "confirmReport", "submitReport", "reportData", "reportedUserId", "reportedUserName", "reason", "reportType", "description", "loadMoreMessages", "resendMessage", "chooseLocation", "chooseFile"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+L;AAC/L,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAA+uB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2RnwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;;IAEA;IACA;MACA;MACA;MACA,wCACAC,qCACA;IACA;MACA;MACA;MACA;IACA;IAEAD;MACAxC;MACAC;MACAC;IACA;;IAEA;IACA;;IAEA;IACA;IAEA;IACA;EACA;EAEAwC;IACAC;MACA,OACA,oCACA,gCACA;IAEA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACA;IAEAC;MAAA;MACA;QACA;MACA;MAEAR;;MAEA;MACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACAA;MACAA;MACAA;;MAEA;MACA;MACAA;;MAEA;MACA;QACAS;QACAC;QAAA;QACAC;UACAX;QACA;QACAY;UACAZ;UACAA;UACAA;UACAA;UACAA;;UAEA;UACA;;UAEA;UACAa;YACAC;YACAC;YACAC;UACA;UAEA;QACA;MACA;;MAEA;MACA;QACAhB;QACA;QACA;QACA;;QAEA;QACAa;UACAC;UACAC;UACAC;QACA;;QAEA;QACA;MACA;;MAEA;MACA;QACAhB;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACAiB;MAAA;MACAjB;MACA;MACA;;MAEA;MACAa;QACAC;QACAC;QACAC;MACA;;MAEA;MACAE;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACA1B;IACA;IAEA;IACA2B;MACA,eACA,YACA,WACA,UACA,UACA,mCACAC,wEACA,WACA,SACA;MAEA;MAEA;QACAR;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACA1B;IACA;IAEA;IACA6B;MACA;MACA7B;MAEAa;QACAJ;QACAqB;QACAnB;UACAX;QACA;QACAY;UACAZ;QACA;MACA;IACA;IAEA+B;MACA/B;MAEA;MACA;MAEA;QACAgC;QACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACAjC;QACA;MACA;MAEA;QACAgC;MACA;MAEA;MACAhC;MAEA;QACA;QACA;MACA;IACA;IAEAkC;MAAA;MACA;MACA;QACA;UACA;YACA3E;cACA4E;cACAC;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;QACAC;QACA;MACA;IACA;IAEAC;MACA;MACA;;MAEA;MACA,IACAC,IACAC,EACA;QACA;QACAzC;QACA;MACA,SAIA;IACA;IAEA0C;MACA;QACA;UACAnF;YACA4E;YACA3E;YACAmF;UACA;QACA;MACA;IACA;IAEAC;MACA;QACA;QACA5C;QAEA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACAA;QAAA;MAEA;QACAA;MACA;IACA;IAEA6C;MACA;MACA;QACA;MACA;MAEA;QACAzB;QACAe;QACAX;QACAsB;QACAtE;QACA4D;QACAW;QACAC;QACAC;MACA;;MAEA;MACA;QACA;QACA;UACAC;QACA;UACA;UACAA;QACA;QACA;QACA;UACAA;QACA;MACA;MAEA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;MACAnD;IACA;IAEAoD;MACA;MACA,8CACA;QAAA;MAAA,EACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACAzE;QACAA;MACA;QACAkB;MACA;IACA;IAEA;IAEAwD;MACA;MACA;MACA;QACA,iDACA,wDACA;MACA;QACA;MACA;MAEAxD;IACA;IAEAyD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAzD;gBAAA;gBAGA;gBACA0D;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA1D;gBACAa;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAf;gBAAA;gBAAA,OAEA;kBACA2D;kBACAC;gBACA;cAAA;gBAHAC;gBAKA7D;;gBAEA;gBACA8D;gBACA,IACAD,UACAA,qBACAA,eACAE,4BACA;kBACAD;gBACA;kBACAA;gBACA;kBACA9D;kBACA8D;gBACA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA,8BACAE;kBACA;kBACA,eACA,gBACA,+BACA,mCACA;;kBAEA;oBACA5C;oBACAe;oBACAX;oBACAsB;oBACAtE,QACA0E,qCACA,4BACA;oBACAd;oBACAW;oBACAC;kBACA;;kBAEA;kBACA;oBACAiB;oBACA;oBACA;sBACAA;oBACA;sBACA;sBACAA,sCACAC,6BACA;oBACA;oBACA;oBACA;sBACAD;oBACA;kBACA;kBAEA;gBACA,GACAE;;gBAEAnE;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAa;kBACAC;kBACAC;gBACA;gBACA;cAAA;gBAGA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqD;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAZ;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;kBACArC;kBACAC;gBACA;cAAA;gBACAtB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuE;MACA;MACA;MACA;MAEA;QACA;QACA,sBACAC,WACAC,WACAC,mCACAC,aACAF,WACAC;MACA;QACA,8FACAF,WACAC,WACAC,mCACAC,aACAF,WACAC;MACA;IACA;IAEAE;MAAA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBACAC,qBAEA;gBACA;;gBAEA;gBACA;gBAEA7B;kBACA9B;kBACAe;kBACAX;kBACAsB;kBACAV;kBACAW;kBACAC;gBACA;gBAEA;gBACA;gBACA;gBAAA;gBAGA;gBACAU,8CAEA;gBAAA;gBAAA,OACA;kBACApC;kBACAC;kBAAA;kBACAC;gBACA;cAAA;gBAJAqC;gBAMA7D;gBAEA;kBACA;kBACAkD;kBACAA;;kBAEA;kBACA;oBACA;sBACA3F;wBACA4E;wBACA5E;0BACA6D;0BACAC;0BACAC;0BACAC;0BACAC;0BACAC;wBACA;sBACA;oBACA;oBACAzB;kBACA;kBAEAA;gBACA;kBACAkD;kBACAlD;kBACAa;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;gBACAkD;gBACArC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiE;MAAA;MACAnE;QACAoE;QACAC;QACAC;QACAxE;UACA;YACAS;YACAe;YACAX;YACAsB;YACAV;YACAW;YACAC;UACA;UAEA;UACA;UACA;;UAEA;UACA9B;YACAgC;UACA;QACA;MACA;IACA;IAEAkC;MACAvE;QACAwE;QACA1B;MACA;IACA;IAEA2B;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IAEA;IACAC;MAAA;MACA;;MAEA;MACA;QACAxF;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACA;;QAEA;QACA;MACA;;MAEA;MACA;QACAA;QACA;QACAa;UACAC;UACAC;QACA;MACA;IACA;IAEA0E;MAAA;MACAzF;MACA;;MAEA;MACAa;QACA6E;QACA/E;UACA;UACA;YACAK;YAAA;YACA2E;YACAC;YACAC;YACAC;YACAC;UACA;QACA;QACAnF;UACA;UACAC;YACAC;YACAU;YACAwE;YACArF;cACA;gBACAE;cACA;YACA;UACA;QACA;MACA;IACA;IAEAoF;MACA;MAEAjG;MACA;;MAEA;MACA;MACA;QACAa;UACAC;UACAC;QACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEAmF;MACAlG;MACA;MACA;IACA;IAEA;IACAmG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAnG;kBAAAoG;kBAAApF;gBAAA;;gBAEA;gBACAqF;kBACAjF;kBACAe;kBACAX;kBACAR;kBACA8B;kBACAV;kBACAW;kBACAC;kBACAC;gBACA;gBAEA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAqD;gBACAtG;;gBAEA;gBACA0D;gBAAA;gBAAA,OACA;kBACApC;kBACAC;kBAAA;kBACAC;kBACA+E;kBACAC;kBACAC;gBACA;cAAA;gBAPA5C;gBASA7D;gBAEA;kBACA;kBACAqG;kBACAA;kBACAA;;kBAEA;kBACA;oBACA;sBACA9I;wBACA4E;wBACA5E;0BACA6D;0BACAC;0BACAC;0BACAC;0BACAC;0BACA+E;0BACAC;0BACA/E;wBACA;sBACA;oBACA;oBACAzB;kBACA;kBAEAA;gBACA;kBACAqG;kBACArG;kBACAa;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAf;gBACAqG;gBACAxF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2F;MAAA;MACA;QACA;QACA;QACA;QAEA1G;QAEAa;UACAJ;UACAkG;UACArJ;UACAsJ;YACAC;UACA;UACAC;YACAC;YACAC;UACA;UACArG;YACAX;YAEA;cACA;cACAA;cAEA;gBACAiH;kBACAxG;kBACAmD;gBACA;cACA;gBACA5D;gBACAkH;cACA;YACA;cACAlH;cACAkH;YACA;UACA;UACAtG;YACAZ;YACAkH;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAnH;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;UACAoH;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACAlE;MACA;;MAEA;MACA;MACA;;MAEA;MACA;QACAlD;MACA;;MAEA;MACA;QACAA;QACAkD;QACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAlD;QACAkD;QACA;QACArC;UACAC;UACAC;QACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAsG;MACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA,IACA9E,IACAC,EACA;QACA;MACA,SAEA;IACA;IAEA8E;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MACA1H;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA2H;MACA3H;MACA;;MAEA;MACA;IACA;IAEA4H;MACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA/G;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA6G;MACA;MACA7H;IACA;IAEA8H;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;;MAEA;MACA;QACA;UACAxK;YACA4E;YACA5E;cACAoF;cACAnF;cACAa;YACA;UACA;QACA;MACA;;MAEA;MACA;QACA2D;MACA;MAEA;QACA;MACA;IACA;IAEA;IACAgG;MACA;MAEA;MAEA;QACAhG;QACA;MACA;;MAEA;MACA;QACA;UACAzE;YACA4E;YACA5E;cACAoF;cACAnF;cACAa;YACA;UACA;QACA;MACA;IACA;IAEA4J;MACApH;IACA;IAEAqH;MACArH;QACAsH;MACA;IACA;IAEAC;MAAA;MACAvH;QACAwH;QACA1H;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACA2H;MACAzH;QACAJ;MACA;IACA;IAEA;IACA8H;MAAA;MACA1H;QACAC;QACAU;QACAwE;QACAwC;QACA7H;UACA;YACA;YACA;;YAEA;YACAE;cACAC;cACAC;cACAC;YACA;;YAEA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAyH;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA5E;gBAEA;kBACA7D;gBACA;kBACAA;kBACAa;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBACAa;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0H;MAAA;MACA,qBACA,UACA,UACA,QACA,QACA,SACA;MAEA7H;QACAwH;QACA1H;UACA;UACA;QACA;MACA;IACA;IAEA;IACAgI;MAAA;MACA9H;QACAC;QACAU;QACAwE;QACAwC;QACA7H;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA/H;kBACAC;gBACA;gBAAA;gBAGA+H;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA9G;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAyB;gBAEAhD;gBAEA;kBACAA;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACAhB;gBACA;kBACAA;kBACAa;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACAb;gBACAa;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAmI;MACA;IAAA,CACA;IAEAC;MACAlG;MACAhC;QACAgC;MACA;IACA;IAEAmG;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxtDA;AAAA;AAAA;AAAA;AAAk6C,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACAt7C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesSub/social/chat/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesSub/social/chat/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=7a033bbb&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a033bbb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesSub/social/chat/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7a033bbb&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-avatar/u-avatar\" */ \"@/components/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-loading/u-loading\" */ \"@/components/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.messageList, function (message, __i0__) {\n    var $orig = _vm.__get_orig(message)\n    var m0 = message.showTime ? _vm.formatMessageTime(message.timestamp) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.voiceMode ? _vm.inputText && _vm.inputText.length > 400 : null\n  var g1 = !_vm.voiceMode && g0 ? _vm.inputText.length : null\n  var g2 = !_vm.voiceMode && g0 ? _vm.inputText.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"chat-container\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <view class=\"header-left\">\n          <view class=\"avatar-container\">\n            <u-avatar\n              :src=\"otherUserAvatar\"\n              size=\"50\"\n              class=\"header-avatar\"\n              @click=\"viewUserProfile\"\n            ></u-avatar>\n            <view v-if=\"isOnline\" class=\"online-indicator\"></view>\n          </view>\n          <view class=\"chat-info\" @click=\"viewUserProfile\">\n            <view class=\"name-container\">\n              <text class=\"chat-name\">{{ chatName }}</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"header-actions\">\n          <u-icon name=\"phone\" size=\"24\" color=\"#333\" @click=\"makeCall\"></u-icon>\n          <u-icon name=\"more-dot-fill\" size=\"24\" color=\"#333\" @click=\"showMoreActions\"></u-icon>\n        </view>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view\n      class=\"message-list\"\n      scroll-y\n      :scroll-top=\"scrollTop\"\n      @scrolltoupper=\"loadMoreMessages\"\n    >\n      <view class=\"message-item\" v-for=\"message in messageList\" :key=\"message.id\">\n        <!-- 时间分割线 -->\n        <view v-if=\"message.showTime\" class=\"time-divider\">\n          <text class=\"time-text\">{{ formatMessageTime(message.timestamp) }}</text>\n        </view>\n\n        <!-- 消息内容 -->\n        <view class=\"message-wrapper\" :class=\"{ 'is-mine': message.isMine }\">\n          <!-- 对方消息：左边头像，右边内容 -->\n          <template v-if=\"!message.isMine\">\n            <u-avatar\n              :src=\"message.avatar\"\n              size=\"64\"\n              class=\"message-avatar\"\n              @click=\"viewUserProfile\"\n            ></u-avatar>\n\n            <view class=\"message-content\">\n              <!-- 文字消息 -->\n              <view v-if=\"message.type === 'text'\" class=\"message-bubble text-bubble\">\n                <text class=\"message-text\">{{ message.content }}</text>\n              </view>\n\n              <!-- 图片消息 -->\n              <view\n                v-else-if=\"message.type === 'image'\"\n                class=\"message-bubble image-bubble\"\n                @click=\"previewImage(message.content)\"\n              >\n                <image :src=\"message.content\" class=\"message-image\" mode=\"aspectFill\" />\n              </view>\n\n              <!-- 语音消息 -->\n              <view\n                v-else-if=\"message.type === 'voice'\"\n                class=\"message-bubble voice-bubble\"\n                @click=\"playVoice(message)\"\n              >\n                <u-icon name=\"volume-fill\" size=\"16\" color=\"#fff\"></u-icon>\n                <text class=\"voice-duration\">{{ message.duration }}''</text>\n                <view v-if=\"message.isPlaying\" class=\"voice-animation\">\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                </view>\n              </view>\n            </view>\n          </template>\n\n          <!-- 我的消息：左边内容，右边头像 -->\n          <template v-else>\n            <view class=\"message-content\">\n              <!-- 文字消息 -->\n              <view v-if=\"message.type === 'text'\" class=\"message-bubble text-bubble mine\">\n                <text class=\"message-text\">{{ message.content }}</text>\n              </view>\n\n              <!-- 图片消息 -->\n              <view\n                v-else-if=\"message.type === 'image'\"\n                class=\"message-bubble image-bubble\"\n                @click=\"previewImage(message.content)\"\n              >\n                <image :src=\"message.content\" class=\"message-image\" mode=\"aspectFill\" />\n              </view>\n\n              <!-- 语音消息 -->\n              <view\n                v-else-if=\"message.type === 'voice'\"\n                class=\"message-bubble voice-bubble mine\"\n                @click=\"playVoice(message)\"\n              >\n                <u-icon name=\"volume-fill\" size=\"16\" color=\"#fff\"></u-icon>\n                <text class=\"voice-duration\">{{ message.duration }}''</text>\n                <view v-if=\"message.isPlaying\" class=\"voice-animation\">\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                  <view class=\"wave\"></view>\n                </view>\n              </view>\n\n              <!-- 消息状态 -->\n              <view class=\"message-status\">\n                <u-icon v-if=\"message.status === 'sending'\" name=\"loading\" size=\"12\" color=\"#999\"></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'sent'\"\n                  name=\"checkmark\"\n                  size=\"12\"\n                  color=\"#999\"\n                ></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'read'\"\n                  name=\"checkmark-done\"\n                  size=\"12\"\n                  color=\"#2979ff\"\n                ></u-icon>\n                <u-icon\n                  v-else-if=\"message.status === 'failed'\"\n                  name=\"close-circle\"\n                  size=\"12\"\n                  color=\"#ff4757\"\n                  @click=\"resendMessage(message)\"\n                ></u-icon>\n              </view>\n            </view>\n          </template>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 输入区域 -->\n    <view class=\"input-area\">\n      <!-- 扩展功能面板 -->\n      <view v-if=\"showExtensions\" class=\"extensions-panel\">\n        <view class=\"extension-grid\">\n          <view class=\"extension-item\" @click=\"chooseImage\">\n            <view class=\"extension-icon photo\">\n              <u-icon name=\"camera\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">照片</text>\n          </view>\n\n          <view class=\"extension-item\" @click=\"startVoiceRecord\">\n            <view class=\"extension-icon voice\">\n              <u-icon name=\"mic\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">语音</text>\n          </view>\n\n          <view class=\"extension-item\" @click=\"chooseLocation\">\n            <view class=\"extension-icon location\">\n              <u-icon name=\"map-pin\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">位置</text>\n          </view>\n\n          <view class=\"extension-item\" @click=\"chooseFile\">\n            <view class=\"extension-icon file\">\n              <u-icon name=\"folder\" color=\"#fff\" size=\"24\"></u-icon>\n            </view>\n            <text class=\"extension-text\">文件</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 表情面板 -->\n      <view v-if=\"showEmojis\" class=\"emoji-panel\">\n        <scroll-view class=\"emoji-scroll\" scroll-y>\n          <view class=\"emoji-grid\">\n            <text\n              v-for=\"emoji in emojiList\"\n              :key=\"emoji\"\n              class=\"emoji-item\"\n              @click=\"insertEmoji(emoji)\"\n            >{{ emoji }}</text>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 输入栏 -->\n      <view class=\"input-bar\" :class=\"{ 'input-focused': inputFocused }\">\n        <!-- 语音按钮 -->\n        <view class=\"voice-btn\" @click=\"toggleVoiceMode\" v-if=\"!inputText && !voiceMode\">\n          <u-icon name=\"mic\" size=\"22\" color=\"#666\"></u-icon>\n        </view>\n\n        <!-- 文本输入区域 -->\n        <view class=\"input-wrapper\" v-if=\"!voiceMode\" :class=\"{ 'has-text': inputText }\">\n          <textarea\n            v-model=\"inputText\"\n            class=\"input-textarea\"\n            placeholder=\"输入消息...\"\n            :maxlength=\"500\"\n            :auto-height=\"true\"\n            :cursor-spacing=\"10\"\n            :adjust-position=\"true\"\n            :show-confirm-bar=\"false\"\n            :disable-default-padding=\"true\"\n            @focus=\"onInputFocus\"\n            @blur=\"onInputBlur\"\n            @input=\"onInputChange\"\n            @linechange=\"onLineChange\"\n            @confirm=\"onInputConfirm\"\n          />\n\n          <!-- 字数统计 -->\n          <view class=\"char-count\" v-if=\"inputText && inputText.length > 400\">\n            <text :class=\"{ 'over-limit': inputText.length > 500 }\">{{ inputText.length }}/500</text>\n          </view>\n        </view>\n\n        <!-- 语音录制按钮 -->\n        <view\n          v-else\n          class=\"voice-record-btn\"\n          :class=\"{\n            'recording': isRecording,\n            'record-ready': !isRecording\n          }\"\n          @touchstart=\"startRecord\"\n          @touchend=\"stopRecord\"\n          @touchcancel=\"cancelRecord\"\n        >\n          <view class=\"record-icon\">\n            <u-icon\n              :name=\"isRecording ? 'pause-circle-fill' : 'mic-fill'\"\n              :color=\"isRecording ? '#fff' : '#666'\"\n              size=\"20\"\n            ></u-icon>\n          </view>\n          <text class=\"record-text\">{{ isRecording ? '松开发送' : '按住说话' }}</text>\n          <view v-if=\"isRecording\" class=\"record-wave\">\n            <view class=\"wave-dot\" v-for=\"i in 3\" :key=\"i\"></view>\n          </view>\n        </view>\n\n        <!-- 操作按钮区域 -->\n        <view class=\"input-actions\">\n          <!-- 表情按钮 -->\n          <view class=\"action-btn\" @click=\"toggleEmojis\">\n            <u-icon name=\"emoji\" size=\"22\" :color=\"showEmojis ? '#2979ff' : '#666'\"></u-icon>\n          </view>\n\n          <!-- 扩展功能按钮 -->\n          <view class=\"action-btn\" @click=\"toggleExtensions\" v-if=\"!inputText && !voiceMode\">\n            <u-icon name=\"plus\" size=\"22\" :color=\"showExtensions ? '#2979ff' : '#666'\"></u-icon>\n          </view>\n\n          <!-- 发送按钮 -->\n          <view\n            class=\"send-btn\"\n            :class=\"{\n              'can-send': canSendMessage,\n              'sending': isSending\n            }\"\n            @click=\"sendMessage\"\n            v-if=\"inputText || voiceMode\"\n          >\n            <u-icon v-if=\"!isSending\" name=\"send\" color=\"#fff\" size=\"18\"></u-icon>\n            <u-loading v-else mode=\"circle\" size=\"16\" color=\"#fff\"></u-loading>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport {\n  getConversationMessages,\n  sendMessage,\n  markMessageAsRead,\n  clearChatHistory,\n  reportUser\n} from \"@/utils/socialApi.js\";\n\nexport default {\n  name: \"ChatDetail\",\n  data() {\n    return {\n      chatId: \"\",\n      chatName: \"\",\n      otherUserAvatar: \"https://picsum.photos/100/100?random=800\",\n      isOnline: true,\n      messageList: [],\n      inputText: \"\",\n      scrollTop: 0,\n      showExtensions: false,\n      showEmojis: false,\n      voiceMode: false,\n      isRecording: false,\n      inputFocused: false,\n      isSending: false,\n      isTyping: false,\n      typingTimer: null,\n      currentUser: {\n        avatar: \"\"\n      },\n      // 语音录制相关\n      recorderManager: null,\n      voiceRecordPath: \"\",\n      voiceRecordDuration: 0,\n      recordStartTime: 0,\n      currentPlayingVoice: null,\n      innerAudioContext: null,\n      emojiList: [\n        \"😀\",\n        \"😃\",\n        \"😄\",\n        \"😁\",\n        \"😆\",\n        \"😅\",\n        \"😂\",\n        \"🤣\",\n        \"😊\",\n        \"😇\",\n        \"🙂\",\n        \"🙃\",\n        \"😉\",\n        \"😌\",\n        \"😍\",\n        \"🥰\",\n        \"😘\",\n        \"😗\",\n        \"😙\",\n        \"😚\",\n        \"😋\",\n        \"😛\",\n        \"😝\",\n        \"😜\",\n        \"🤪\",\n        \"🤨\",\n        \"🧐\",\n        \"🤓\",\n        \"😎\",\n        \"🤩\",\n        \"🥳\",\n        \"😏\"\n      ],\n      // uview输入框自定义样式\n      inputCustomStyle: {\n        backgroundColor: \"transparent\",\n        fontSize: \"32rpx\",\n        lineHeight: \"1.4\",\n        minHeight: \"40rpx\",\n        maxHeight: \"200rpx\",\n        padding: \"0\",\n        color: \"#333\"\n      },\n      // placeholder样式\n      placeholderStyle: \"color: #999; font-size: 32rpx;\",\n\n      // WebSocket相关\n      socketTask: null,\n      isConnected: false,\n      reconnectTimer: null,\n      reconnectCount: 0,\n      maxReconnectCount: 5,\n      heartbeatTimer: null\n    };\n  },\n  onLoad(options) {\n    console.log(\"聊天页面参数:\", options);\n\n    // 支持从用户主页跳转的参数\n    if (options.userId) {\n      this.chatId = options.userId;\n      this.chatName = decodeURIComponent(options.nickname || \"用户\");\n      this.otherUserAvatar = options.avatar\n        ? decodeURIComponent(options.avatar)\n        : \"https://picsum.photos/100/100?random=800\";\n    } else {\n      // 兼容原有的参数格式\n      this.chatId = options.id;\n      this.chatName = options.name || \"聊天\";\n    }\n\n    console.log(\"聊天对象信息:\", {\n      chatId: this.chatId,\n      chatName: this.chatName,\n      otherUserAvatar: this.otherUserAvatar\n    });\n\n    // 初始化语音录制管理器\n    this.initRecorderManager();\n\n    // 加载当前用户头像\n    this.loadCurrentUserInfo();\n\n    this.loadMessages();\n    this.connectWebSocket();\n  },\n\n  computed: {\n    canSendMessage() {\n      return (\n        this.inputText.trim().length > 0 &&\n        this.inputText.length <= 500 &&\n        !this.isSending\n      );\n    }\n  },\n\n  onUnload() {\n    this.disconnectWebSocket();\n    this.cleanupAudio();\n  },\n\n  onHide() {\n    this.disconnectWebSocket();\n    this.cleanupAudio();\n  },\n\n  onShow() {\n    if (!this.isConnected) {\n      this.connectWebSocket();\n    }\n  },\n\n  methods: {\n    // ==================== WebSocket相关方法 ====================\n\n    connectWebSocket() {\n      if (this.isConnected || this.socketTask) {\n        return;\n      }\n\n      console.log(\"开始连接WebSocket...\");\n\n      // 获取当前用户ID和token\n      const currentUserId = uni.getStorageSync(\"userid\");\n      const token = uni.getStorageSync(\"token\");\n\n      if (!currentUserId || !token) {\n        console.error(\"缺少用户ID或token，无法连接WebSocket\");\n        return;\n      }\n\n      // 检查开发者工具设置\n      console.log(\"📱 当前环境信息:\");\n      console.log(\"- 平台:\", uni.getSystemInfoSync().platform);\n      console.log(\"- 环境:\", process.env.NODE_ENV);\n\n      // 构建WebSocket URL\n      const wsUrl = `${this.getWebSocketUrl()}?userId=${currentUserId}&token=${token}`;\n      console.log(\"🔗 尝试连接WebSocket:\", wsUrl);\n\n      // 尝试不同的连接方式\n      this.socketTask = uni.connectSocket({\n        url: wsUrl,\n        protocols: [], // 明确指定协议\n        success: () => {\n          console.log(\"✅ WebSocket连接请求发送成功\");\n        },\n        fail: error => {\n          console.error(\"❌ WebSocket连接失败:\", error);\n          console.error(\"连接URL:\", wsUrl);\n          console.error(\"用户ID:\", currentUserId);\n          console.error(\"Token:\", token);\n          console.error(\"错误详情:\", JSON.stringify(error));\n\n          // 尝试使用HTTP测试连接性\n          this.testHttpConnection();\n\n          // 显示用户友好的错误信息\n          uni.showToast({\n            title: \"WebSocket连接失败\",\n            icon: \"none\",\n            duration: 3000\n          });\n\n          this.scheduleReconnect();\n        }\n      });\n\n      // 监听WebSocket连接打开\n      this.socketTask.onOpen(() => {\n        console.log(\"🎉 WebSocket连接已打开\");\n        this.isConnected = true;\n        this.reconnectCount = 0;\n        this.startHeartbeat();\n\n        // 显示连接成功提示\n        uni.showToast({\n          title: \"连接成功\",\n          icon: \"success\",\n          duration: 2000\n        });\n\n        // 加入聊天房间\n        this.joinChatRoom();\n      });\n\n      // 监听WebSocket消息\n      this.socketTask.onMessage(res => {\n        console.log(\"收到WebSocket消息:\", res.data);\n        this.handleWebSocketMessage(res.data);\n      });\n\n      // 监听WebSocket连接关闭\n      this.socketTask.onClose(() => {\n        console.log(\"WebSocket连接已关闭\");\n        this.isConnected = false;\n        this.stopHeartbeat();\n        this.scheduleReconnect();\n      });\n\n      // 监听WebSocket错误\n      this.socketTask.onError(error => {\n        console.error(\"WebSocket错误:\", error);\n        this.isConnected = false;\n        this.scheduleReconnect();\n      });\n    },\n\n    // 模拟WebSocket连接（用于测试）\n    simulateWebSocketConnection() {\n      console.log(\"🔧 模拟WebSocket连接成功\");\n      this.isConnected = true;\n      this.reconnectCount = 0;\n\n      // 模拟连接成功\n      uni.showToast({\n        title: \"连接成功（模拟）\",\n        icon: \"success\",\n        duration: 2000\n      });\n\n      // 模拟接收消息（用于测试）\n      setTimeout(() => {\n        this.simulateReceiveMessage();\n      }, 3000);\n    },\n\n    // 模拟接收消息\n    simulateReceiveMessage() {\n      const mockMessage = {\n        id: Date.now(),\n        senderId: this.chatId, // 对方ID\n        receiverId: uni.getStorageSync(\"userid\"),\n        messageType: 1,\n        content: \"这是一条模拟接收的消息，用于测试聊天界面功能！\",\n        createTime: new Date().toISOString(),\n        isRead: false\n      };\n\n      this.handleNewMessage(mockMessage);\n      console.log(\"📨 模拟接收到消息:\", mockMessage);\n    },\n\n    // 模拟对方回复\n    simulateReply(originalMessage) {\n      const replies = [\n        \"收到你的消息了！\",\n        \"好的，我知道了\",\n        \"谢谢你的分享\",\n        \"这个想法不错\",\n        \"我也是这么想的\",\n        `关于\"${originalMessage}\"，我觉得很有意思`,\n        \"让我想想...\",\n        \"同意你的观点\"\n      ];\n\n      const randomReply = replies[Math.floor(Math.random() * replies.length)];\n\n      const replyMessage = {\n        id: Date.now(),\n        senderId: this.chatId, // 对方ID\n        receiverId: uni.getStorageSync(\"userid\"),\n        messageType: 1,\n        content: randomReply,\n        createTime: new Date().toISOString(),\n        isRead: false\n      };\n\n      this.handleNewMessage(replyMessage);\n      console.log(\"🤖 模拟对方回复:\", randomReply);\n    },\n\n    // 测试HTTP连接\n    testHttpConnection() {\n      const testUrl = \"http://192.168.1.21:8101/api/\";\n      console.log(\"🔍 测试HTTP连接:\", testUrl);\n\n      uni.request({\n        url: testUrl,\n        method: \"GET\",\n        success: res => {\n          console.log(\"✅ HTTP连接成功:\", res);\n        },\n        fail: error => {\n          console.error(\"❌ HTTP连接失败:\", error);\n        }\n      });\n    },\n\n    disconnectWebSocket() {\n      console.log(\"断开WebSocket连接\");\n\n      this.isConnected = false;\n      this.stopHeartbeat();\n\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer);\n        this.reconnectTimer = null;\n      }\n\n      if (this.socketTask) {\n        this.socketTask.close();\n        this.socketTask = null;\n      }\n    },\n\n    scheduleReconnect() {\n      if (this.reconnectCount >= this.maxReconnectCount) {\n        console.log(\"达到最大重连次数，停止重连\");\n        return;\n      }\n\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer);\n      }\n\n      const delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒\n      console.log(`${delay}ms后尝试重连 (第${this.reconnectCount + 1}次)`);\n\n      this.reconnectTimer = setTimeout(() => {\n        this.reconnectCount++;\n        this.connectWebSocket();\n      }, delay);\n    },\n\n    startHeartbeat() {\n      this.stopHeartbeat();\n      this.heartbeatTimer = setInterval(() => {\n        if (this.isConnected && this.socketTask) {\n          this.socketTask.send({\n            data: JSON.stringify({\n              type: \"heartbeat\",\n              timestamp: Date.now()\n            })\n          });\n        }\n      }, 30000); // 30秒心跳\n    },\n\n    stopHeartbeat() {\n      if (this.heartbeatTimer) {\n        clearInterval(this.heartbeatTimer);\n        this.heartbeatTimer = null;\n      }\n    },\n\n    getWebSocketUrl() {\n      // 根据环境返回WebSocket URL\n      const systemInfo = uni.getSystemInfoSync();\n\n      // 检查是否为开发环境\n      if (\n        process.env.NODE_ENV === \"development\" ||\n        systemInfo.platform === \"devtools\"\n      ) {\n        // 开发环境使用本地WebSocket服务（端口8102）\n        console.log(\"🔧 开发环境，使用本地WebSocket服务\");\n        return \"ws://localhost:8101/api/ws/chat\";\n      } else {\n        // 生产环境使用服务器WebSocket\n        console.log(\"🌐 生产环境，使用服务器WebSocket服务\");\n        return \"wss://admin.foxdance.com.cn/ws/chat\";\n      }\n    },\n\n    joinChatRoom() {\n      if (this.isConnected && this.socketTask) {\n        this.socketTask.send({\n          data: JSON.stringify({\n            type: \"join\",\n            chatId: this.chatId,\n            userId: uni.getStorageSync(\"userid\")\n          })\n        });\n      }\n    },\n\n    handleWebSocketMessage(data) {\n      try {\n        const message = JSON.parse(data);\n        console.log(\"处理WebSocket消息:\", message);\n\n        switch (message.type) {\n          case \"message\":\n            this.handleNewMessage(message.data);\n            break;\n          case \"typing\":\n            this.handleTypingStatus(message.data);\n            break;\n          case \"read\":\n            this.handleMessageRead(message.data);\n            break;\n          case \"online\":\n            this.handleOnlineStatus(message.data);\n            break;\n          default:\n            console.log(\"未知消息类型:\", message.type);\n        }\n      } catch (error) {\n        console.error(\"解析WebSocket消息失败:\", error);\n      }\n    },\n\n    handleNewMessage(messageData) {\n      // 只处理对方发送的消息\n      if (messageData.senderId != this.chatId) {\n        return;\n      }\n\n      const message = {\n        id: messageData.id,\n        type: this.getMessageTypeString(messageData.messageType),\n        content: messageData.content,\n        isMine: false,\n        avatar: this.otherUserAvatar,\n        timestamp: new Date(messageData.createTime),\n        showTime: this.shouldShowTime(messageData.createTime),\n        status: \"sent\",\n        isPlaying: false\n      };\n\n      // 如果是语音消息，添加duration字段\n      if (messageData.messageType === 3) {\n        // 优先使用mediaDuration字段，否则从content中提取时长\n        if (messageData.mediaDuration) {\n          message.duration = messageData.mediaDuration;\n        } else {\n          const durationMatch = messageData.content.match(/(\\d+)秒/);\n          message.duration = durationMatch ? parseInt(durationMatch[1]) : 1;\n        }\n        // 如果有mediaUrl，使用mediaUrl作为播放地址\n        if (messageData.mediaUrl) {\n          message.content = messageData.mediaUrl;\n        }\n      }\n\n      this.messageList.push(message);\n      this.scrollToBottom();\n\n      // 播放消息提示音\n      this.playMessageSound();\n    },\n\n    handleTypingStatus(data) {\n      // 处理对方正在输入状态\n      console.log(\"对方正在输入:\", data);\n    },\n\n    handleMessageRead(data) {\n      // 处理消息已读状态\n      const messageIndex = this.messageList.findIndex(\n        msg => msg.id === data.messageId\n      );\n      if (messageIndex !== -1) {\n        this.messageList[messageIndex].status = \"read\";\n      }\n    },\n\n    handleOnlineStatus(data) {\n      // 处理在线状态\n      this.isOnline = data.isOnline;\n    },\n\n    shouldShowTime(timestamp) {\n      if (this.messageList.length === 0) return true;\n\n      const lastMessage = this.messageList[this.messageList.length - 1];\n      const timeDiff = new Date(timestamp) - lastMessage.timestamp;\n      return timeDiff > 300000; // 5分钟\n    },\n\n    playMessageSound() {\n      // 播放消息提示音\n      try {\n        const innerAudioContext = uni.createInnerAudioContext();\n        innerAudioContext.src = \"/static/sounds/message.mp3\";\n        innerAudioContext.play();\n      } catch (error) {\n        console.log(\"播放提示音失败:\", error);\n      }\n    },\n\n    // ==================== 原有方法 ====================\n\n    loadCurrentUserInfo() {\n      // 从缓存中获取当前用户信息\n      const userInfo = uni.getStorageSync(\"user\");\n      if (userInfo && userInfo.data) {\n        this.currentUser.avatar = userInfo.data.avatar\n          ? \"https://file.foxdance.com.cn\" + userInfo.data.avatar\n          : \"/static/images/toux.png\";\n      } else {\n        this.currentUser.avatar = \"/static/images/toux.png\";\n      }\n\n      console.log(\"当前用户头像:\", this.currentUser.avatar);\n    },\n\n    async loadMessages() {\n      console.log(\"开始加载聊天消息，对方用户ID:\", this.chatId);\n\n      try {\n        // 获取当前用户ID\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) {\n          console.error(\"当前用户ID无效，无法加载消息\");\n          uni.showToast({\n            title: \"请先登录\",\n            icon: \"none\"\n          });\n          return;\n        }\n\n        console.log(\"当前用户ID:\", currentUserId, \"对方用户ID:\", this.chatId);\n\n        const result = await getConversationMessages(this.chatId, {\n          current: 1,\n          size: 50\n        });\n\n        console.log(\"聊天消息API返回:\", result);\n\n        // 处理API返回的数据格式\n        let messages = [];\n        if (\n          result &&\n          result.code === 0 &&\n          result.data &&\n          Array.isArray(result.data)\n        ) {\n          messages = result.data;\n        } else if (result && Array.isArray(result)) {\n          messages = result;\n        } else {\n          console.log(\"没有找到聊天消息或格式不正确\");\n          messages = [];\n        }\n\n        if (messages.length > 0) {\n          this.messageList = messages\n            .map((message, index) => {\n              const prevMessage = messages[index - 1];\n              const showTime =\n                !prevMessage ||\n                new Date(message.createTime) -\n                  new Date(prevMessage.createTime) >\n                  300000; // 5分钟\n\n              const messageObj = {\n                id: message.id,\n                type: this.getMessageTypeString(message.messageType),\n                content: message.content,\n                isMine: message.senderId === currentUserId,\n                avatar:\n                  message.senderId === currentUserId\n                    ? this.currentUser.avatar\n                    : this.otherUserAvatar,\n                timestamp: new Date(message.createTime),\n                showTime: showTime,\n                status: message.isRead === 1 ? \"read\" : \"sent\"\n              };\n\n              // 如果是语音消息，添加语音相关字段\n              if (message.messageType === 3) {\n                messageObj.isPlaying = false;\n                // 优先使用mediaDuration字段，否则从content中提取时长\n                if (message.mediaDuration) {\n                  messageObj.duration = message.mediaDuration;\n                } else {\n                  const durationMatch = message.content.match(/(\\d+)秒/);\n                  messageObj.duration = durationMatch\n                    ? parseInt(durationMatch[1])\n                    : 1;\n                }\n                // 如果有mediaUrl，使用mediaUrl作为播放地址\n                if (message.mediaUrl) {\n                  messageObj.content = message.mediaUrl;\n                }\n              }\n\n              return messageObj;\n            })\n            .reverse(); // 消息按时间正序显示\n\n          console.log(\"消息列表处理完成，消息数量:\", this.messageList.length);\n\n          // 标记消息已读\n          await this.markAllMessagesAsRead();\n        } else {\n          console.log(\"没有历史消息，显示空聊天界面\");\n          this.messageList = [];\n        }\n      } catch (error) {\n        console.error(\"加载消息失败:\", error);\n        uni.showToast({\n          title: \"消息加载失败\",\n          icon: \"none\"\n        });\n        this.messageList = [];\n      }\n\n      // 滚动到底部\n      this.$nextTick(() => {\n        this.scrollToBottom();\n      });\n    },\n\n    // 获取当前用户ID\n    getCurrentUserId() {\n      const userId = uni.getStorageSync(\"userid\");\n      return userId ? Number(userId) : null;\n    },\n\n    // 将消息类型数字转换为字符串\n    getMessageTypeString(messageType) {\n      const typeMap = {\n        1: \"text\",\n        2: \"image\",\n        3: \"voice\",\n        4: \"video\"\n      };\n      return typeMap[messageType] || \"text\";\n    },\n\n    // 标记所有消息已读\n    async markAllMessagesAsRead() {\n      try {\n        const currentUserId = this.getCurrentUserId();\n        if (!currentUserId) return;\n\n        await markMessageAsRead({\n          senderId: this.chatId,\n          receiverId: currentUserId\n        });\n        console.log(\"消息已标记为已读\");\n      } catch (error) {\n        console.error(\"标记消息已读失败:\", error);\n      }\n    },\n\n    formatMessageTime(timestamp) {\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diff = now - date;\n\n      if (diff < 86400000) {\n        // 今天\n        return `${date\n          .getHours()\n          .toString()\n          .padStart(2, \"0\")}:${date\n          .getMinutes()\n          .toString()\n          .padStart(2, \"0\")}`;\n      } else {\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date\n          .getHours()\n          .toString()\n          .padStart(2, \"0\")}:${date\n          .getMinutes()\n          .toString()\n          .padStart(2, \"0\")}`;\n      }\n    },\n\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.scrollTop = 999999;\n      });\n    },\n\n    async sendMessage() {\n      if (!this.canSendMessage) return;\n\n      const messageContent = this.inputText.trim();\n      const tempId = Date.now();\n\n      // 设置发送状态\n      this.isSending = true;\n\n      // 停止输入状态提示\n      this.stopTypingIndicator();\n\n      const message = {\n        id: tempId,\n        type: \"text\",\n        content: messageContent,\n        isMine: true,\n        timestamp: new Date(),\n        showTime: this.shouldShowTime(new Date()),\n        status: \"sending\"\n      };\n\n      this.messageList.push(message);\n      this.inputText = \"\";\n      this.scrollToBottom();\n\n      try {\n        // 获取当前用户ID\n        const currentUserId = uni.getStorageSync(\"userid\");\n\n        // 1. 先通过API发送消息到服务器\n        const result = await sendMessage({\n          receiverId: Number(this.chatId),\n          messageType: 1, // 1表示文本消息\n          content: messageContent\n        });\n\n        console.log(\"发送消息API返回:\", result);\n\n        if (result && result.code === 0) {\n          // 更新消息状态和ID\n          message.id = result.data.id || tempId;\n          message.status = \"sent\";\n\n          // 2. 通过WebSocket实时推送消息\n          if (this.isConnected && this.socketTask) {\n            this.socketTask.send({\n              data: JSON.stringify({\n                type: \"message\",\n                data: {\n                  id: message.id,\n                  senderId: currentUserId,\n                  receiverId: this.chatId,\n                  messageType: 1,\n                  content: messageContent,\n                  createTime: new Date().toISOString()\n                }\n              })\n            });\n            console.log(\"消息已通过WebSocket发送\");\n          }\n\n          console.log(\"消息发送成功:\", result.data);\n        } else {\n          message.status = \"failed\";\n          console.error(\"消息发送失败:\", result);\n          uni.showToast({\n            title: result?.message || \"发送失败\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"发送消息失败:\", error);\n        message.status = \"failed\";\n        uni.showToast({\n          title: \"发送失败\",\n          icon: \"none\"\n        });\n      } finally {\n        // 重置发送状态\n        this.isSending = false;\n      }\n    },\n\n    chooseImage() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: res => {\n          const message = {\n            id: Date.now(),\n            type: \"image\",\n            content: res.tempFilePaths[0],\n            isMine: true,\n            timestamp: new Date(),\n            showTime: false,\n            status: \"sending\"\n          };\n\n          this.messageList.push(message);\n          this.showExtensions = false;\n          this.scrollToBottom();\n\n          // 模拟上传成功\n          setTimeout(() => {\n            message.status = \"sent\";\n          }, 1000);\n        }\n      });\n    },\n\n    previewImage(url) {\n      uni.previewImage({\n        urls: [url],\n        current: url\n      });\n    },\n\n    startVoiceRecord() {\n      this.voiceMode = true;\n      this.showExtensions = false;\n    },\n\n    toggleVoiceMode() {\n      this.voiceMode = !this.voiceMode;\n    },\n\n    // ==================== 语音录制相关方法 ====================\n\n    // 初始化录音管理器\n    initRecorderManager() {\n      this.recorderManager = uni.getRecorderManager();\n\n      // 录音开始事件\n      this.recorderManager.onStart(() => {\n        console.log(\"录音开始\");\n        this.recordStartTime = Date.now();\n      });\n\n      // 录音结束事件\n      this.recorderManager.onStop(res => {\n        console.log(\"录音结束\", res);\n        this.voiceRecordPath = res.tempFilePath;\n        this.voiceRecordDuration = Math.floor(res.duration / 1000); // 转换为秒\n\n        // 发送语音消息\n        this.sendVoiceMessage(res.tempFilePath, this.voiceRecordDuration);\n      });\n\n      // 录音错误事件\n      this.recorderManager.onError(err => {\n        console.error(\"录音错误:\", err);\n        this.isRecording = false;\n        uni.showToast({\n          title: \"录音失败\",\n          icon: \"none\"\n        });\n      });\n    },\n\n    startRecord() {\n      console.log(\"开始录音\");\n      this.isRecording = true;\n\n      // 检查录音权限\n      uni.authorize({\n        scope: \"scope.record\",\n        success: () => {\n          // 开始录音\n          this.recorderManager.start({\n            duration: 60000, // 最长60秒\n            sampleRate: 16000,\n            numberOfChannels: 1,\n            encodeBitRate: 96000,\n            format: \"mp3\",\n            frameSize: 50\n          });\n        },\n        fail: () => {\n          this.isRecording = false;\n          uni.showModal({\n            title: \"录音权限\",\n            content: \"需要录音权限才能发送语音消息\",\n            confirmText: \"去设置\",\n            success: res => {\n              if (res.confirm) {\n                uni.openSetting();\n              }\n            }\n          });\n        }\n      });\n    },\n\n    stopRecord() {\n      if (!this.isRecording) return;\n\n      console.log(\"停止录音\");\n      this.isRecording = false;\n\n      // 检查录音时长\n      const recordDuration = Date.now() - this.recordStartTime;\n      if (recordDuration < 1000) {\n        uni.showToast({\n          title: \"录音时间太短\",\n          icon: \"none\"\n        });\n        this.recorderManager.stop();\n        return;\n      }\n\n      // 停止录音\n      this.recorderManager.stop();\n    },\n\n    cancelRecord() {\n      console.log(\"取消录音\");\n      this.isRecording = false;\n      this.recorderManager.stop();\n    },\n\n    // 发送语音消息\n    async sendVoiceMessage(tempFilePath, duration) {\n      console.log(\"准备发送语音消息:\", { tempFilePath, duration });\n\n      // 创建临时消息显示在界面上\n      const tempMessage = {\n        id: Date.now(),\n        type: \"voice\",\n        content: \"\",\n        duration: duration,\n        isMine: true,\n        timestamp: new Date(),\n        showTime: this.shouldShowTime(new Date()),\n        status: \"sending\",\n        isPlaying: false\n      };\n\n      this.messageList.push(tempMessage);\n      this.scrollToBottom();\n\n      try {\n        // 1. 上传语音文件\n        const uploadResult = await this.uploadVoiceFile(tempFilePath);\n        console.log(\"语音文件上传成功:\", uploadResult);\n\n        // 2. 发送消息到服务器\n        const currentUserId = uni.getStorageSync(\"userid\");\n        const result = await sendMessage({\n          receiverId: Number(this.chatId),\n          messageType: 3, // 3表示语音消息\n          content: `语音消息 ${duration}秒`,\n          mediaUrl: uploadResult.url,\n          mediaDuration: duration,\n          mediaSize: uploadResult.size || 0\n        });\n\n        console.log(\"语音消息发送API返回:\", result);\n\n        if (result && result.code === 0) {\n          // 更新消息状态和ID\n          tempMessage.id = result.data.id || tempMessage.id;\n          tempMessage.status = \"sent\";\n          tempMessage.content = uploadResult.url; // 存储语音文件URL\n\n          // 通过WebSocket实时推送消息\n          if (this.isConnected && this.socketTask) {\n            this.socketTask.send({\n              data: JSON.stringify({\n                type: \"message\",\n                data: {\n                  id: tempMessage.id,\n                  senderId: currentUserId,\n                  receiverId: this.chatId,\n                  messageType: 3,\n                  content: uploadResult.url,\n                  mediaUrl: uploadResult.url,\n                  mediaDuration: duration,\n                  createTime: new Date().toISOString()\n                }\n              })\n            });\n            console.log(\"语音消息已通过WebSocket发送\");\n          }\n\n          console.log(\"语音消息发送成功\");\n        } else {\n          tempMessage.status = \"failed\";\n          console.error(\"语音消息发送失败:\", result);\n          uni.showToast({\n            title: result?.message || \"发送失败\",\n            icon: \"none\"\n          });\n        }\n      } catch (error) {\n        console.error(\"发送语音消息失败:\", error);\n        tempMessage.status = \"failed\";\n        uni.showToast({\n          title: \"发送失败\",\n          icon: \"none\"\n        });\n      }\n    },\n\n    // 上传语音文件\n    uploadVoiceFile(tempFilePath) {\n      return new Promise((resolve, reject) => {\n        const token = uni.getStorageSync(\"token\");\n        const currentUserId = uni.getStorageSync(\"userid\");\n        const baseUrl = this.getApiBaseUrl();\n\n        console.log(\"开始上传语音文件:\", tempFilePath);\n\n        uni.uploadFile({\n          url: `${baseUrl}/api/upload/voice`,\n          filePath: tempFilePath,\n          name: \"file\",\n          formData: {\n            driver: \"cos\"\n          },\n          header: {\n            bausertoken: token,\n            userid: currentUserId\n          },\n          success: uploadFileRes => {\n            console.log(\"语音文件上传响应:\", uploadFileRes);\n\n            try {\n              const result = JSON.parse(uploadFileRes.data);\n              console.log(\"语音文件上传结果:\", result);\n\n              if (result.code === 0 && result.data) {\n                resolve({\n                  url: result.data.url || result.data,\n                  size: result.data.size || 0\n                });\n              } else {\n                console.error(\"语音文件上传失败:\", result);\n                reject(new Error(result.message || \"上传失败\"));\n              }\n            } catch (parseError) {\n              console.error(\"解析上传响应失败:\", parseError);\n              reject(new Error(\"解析响应失败\"));\n            }\n          },\n          fail: error => {\n            console.error(\"语音文件上传请求失败:\", error);\n            reject(new Error(\"上传请求失败\"));\n          }\n        });\n      });\n    },\n\n    // 播放语音消息\n    playVoice(message) {\n      console.log(\"播放语音消息:\", message);\n\n      // 停止当前播放的语音\n      if (this.currentPlayingVoice && this.innerAudioContext) {\n        this.innerAudioContext.stop();\n        this.currentPlayingVoice.isPlaying = false;\n      }\n\n      // 停止其他语音播放状态\n      this.messageList.forEach(msg => {\n        if (msg.type === \"voice\") {\n          msg.isPlaying = false;\n        }\n      });\n\n      // 如果点击的是正在播放的语音，则停止播放\n      if (this.currentPlayingVoice === message) {\n        this.currentPlayingVoice = null;\n        return;\n      }\n\n      // 开始播放新的语音\n      message.isPlaying = true;\n      this.currentPlayingVoice = message;\n\n      // 创建音频上下文\n      this.innerAudioContext = uni.createInnerAudioContext();\n      this.innerAudioContext.src = message.content; // content存储的是语音文件URL\n\n      // 播放开始\n      this.innerAudioContext.onPlay(() => {\n        console.log(\"语音开始播放\");\n      });\n\n      // 播放结束\n      this.innerAudioContext.onEnded(() => {\n        console.log(\"语音播放结束\");\n        message.isPlaying = false;\n        this.currentPlayingVoice = null;\n        if (this.innerAudioContext) {\n          this.innerAudioContext.destroy();\n          this.innerAudioContext = null;\n        }\n      });\n\n      // 播放错误\n      this.innerAudioContext.onError(error => {\n        console.error(\"语音播放错误:\", error);\n        message.isPlaying = false;\n        this.currentPlayingVoice = null;\n        uni.showToast({\n          title: \"播放失败\",\n          icon: \"none\"\n        });\n        if (this.innerAudioContext) {\n          this.innerAudioContext.destroy();\n          this.innerAudioContext = null;\n        }\n      });\n\n      // 开始播放\n      this.innerAudioContext.play();\n    },\n\n    // 清理音频资源\n    cleanupAudio() {\n      if (this.innerAudioContext) {\n        this.innerAudioContext.stop();\n        this.innerAudioContext.destroy();\n        this.innerAudioContext = null;\n      }\n      if (this.currentPlayingVoice) {\n        this.currentPlayingVoice.isPlaying = false;\n        this.currentPlayingVoice = null;\n      }\n    },\n\n    // 获取API基础URL\n    getApiBaseUrl() {\n      // 根据环境返回不同的API地址\n      const systemInfo = uni.getSystemInfoSync();\n\n      if (\n        process.env.NODE_ENV === \"development\" ||\n        systemInfo.platform === \"devtools\"\n      ) {\n        return \"http://localhost:8101\";\n      } else {\n        return \"https://admin.foxdance.com.cn\";\n      }\n    },\n\n    toggleEmojis() {\n      this.showEmojis = !this.showEmojis;\n      this.showExtensions = false;\n    },\n\n    toggleExtensions() {\n      this.showExtensions = !this.showExtensions;\n      this.showEmojis = false;\n    },\n\n    insertEmoji(emoji) {\n      this.inputText += emoji;\n    },\n\n    onInputFocus() {\n      console.log(\"输入框获得焦点\");\n      this.inputFocused = true;\n      this.showEmojis = false;\n      this.showExtensions = false;\n\n      // 滚动到底部，确保输入框可见\n      this.$nextTick(() => {\n        this.scrollToBottom();\n      });\n    },\n\n    onInputBlur() {\n      console.log(\"输入框失去焦点\");\n      this.inputFocused = false;\n\n      // 停止输入状态提示\n      this.stopTypingIndicator();\n    },\n\n    onInputChange(e) {\n      const value = e.detail.value;\n      this.inputText = value;\n\n      // 发送正在输入状态\n      this.sendTypingIndicator();\n\n      // 输入验证\n      if (value.length > 500) {\n        uni.showToast({\n          title: \"消息长度不能超过500字\",\n          icon: \"none\",\n          duration: 1500\n        });\n      }\n    },\n\n    onLineChange(e) {\n      // 处理行数变化，可以用于调整输入框高度\n      console.log(\"输入框行数变化:\", e.detail);\n    },\n\n    onInputConfirm() {\n      // 处理键盘确认事件（如果需要支持回车发送）\n      if (this.canSendMessage) {\n        this.sendMessage();\n      }\n    },\n\n    // 发送正在输入状态\n    sendTypingIndicator() {\n      if (this.isTyping) return;\n\n      this.isTyping = true;\n\n      // 通过WebSocket发送正在输入状态\n      if (this.isConnected && this.socketTask) {\n        this.socketTask.send({\n          data: JSON.stringify({\n            type: \"typing\",\n            data: {\n              userId: uni.getStorageSync(\"userid\"),\n              chatId: this.chatId,\n              isTyping: true\n            }\n          })\n        });\n      }\n\n      // 3秒后自动停止输入状态\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n      }\n\n      this.typingTimer = setTimeout(() => {\n        this.stopTypingIndicator();\n      }, 3000);\n    },\n\n    // 停止正在输入状态\n    stopTypingIndicator() {\n      if (!this.isTyping) return;\n\n      this.isTyping = false;\n\n      if (this.typingTimer) {\n        clearTimeout(this.typingTimer);\n        this.typingTimer = null;\n      }\n\n      // 通过WebSocket发送停止输入状态\n      if (this.isConnected && this.socketTask) {\n        this.socketTask.send({\n          data: JSON.stringify({\n            type: \"typing\",\n            data: {\n              userId: uni.getStorageSync(\"userid\"),\n              chatId: this.chatId,\n              isTyping: false\n            }\n          })\n        });\n      }\n    },\n\n    goBack() {\n      uni.navigateBack();\n    },\n\n    makeCall() {\n      uni.makePhoneCall({\n        phoneNumber: \"10086\"\n      });\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: [\"查看资料\", \"清空聊天记录\", \"举报\"],\n        success: res => {\n          switch (res.tapIndex) {\n            case 0:\n              this.viewUserProfile();\n              break;\n            case 1:\n              this.clearChatHistory();\n              break;\n            case 2:\n              this.reportUser();\n              break;\n          }\n        }\n      });\n    },\n\n    // 查看用户资料\n    viewUserProfile() {\n      uni.navigateTo({\n        url: `/pagesSub/social/user/profile?userId=${this.chatId}&name=${this.chatName}`\n      });\n    },\n\n    // 清空聊天记录\n    clearChatHistory() {\n      uni.showModal({\n        title: \"清空聊天记录\",\n        content: \"确定要清空与该用户的所有聊天记录吗？此操作不可恢复。\",\n        confirmText: \"清空\",\n        confirmColor: \"#ff4757\",\n        success: res => {\n          if (res.confirm) {\n            // 清空消息列表\n            this.messageList = [];\n\n            // 显示成功提示\n            uni.showToast({\n              title: \"聊天记录已清空\",\n              icon: \"success\",\n              duration: 2000\n            });\n\n            // 这里可以调用后端API清空服务器端的聊天记录\n            this.clearChatHistoryFromServer();\n          }\n        }\n      });\n    },\n\n    // 从服务器清空聊天记录\n    async clearChatHistoryFromServer() {\n      try {\n        const result = await clearChatHistory(this.chatId);\n\n        if (result && result.code === 0) {\n          console.log(\"服务器聊天记录已清空\");\n        } else {\n          console.error(\"清空聊天记录失败:\", result?.message || \"未知错误\");\n          uni.showToast({\n            title: result?.message || \"清空失败，请重试\",\n            icon: \"error\",\n            duration: 2000\n          });\n        }\n      } catch (error) {\n        console.error(\"清空聊天记录失败:\", error);\n        uni.showToast({\n          title: \"清空失败，请重试\",\n          icon: \"error\",\n          duration: 2000\n        });\n      }\n    },\n\n    // 举报用户\n    reportUser() {\n      const reportReasons = [\n        \"发送垃圾信息\",\n        \"发送不当内容\",\n        \"骚扰他人\",\n        \"诈骗行为\",\n        \"其他违规行为\"\n      ];\n\n      uni.showActionSheet({\n        itemList: reportReasons,\n        success: res => {\n          const reason = reportReasons[res.tapIndex];\n          this.confirmReport(reason);\n        }\n      });\n    },\n\n    // 确认举报\n    confirmReport(reason) {\n      uni.showModal({\n        title: \"举报用户\",\n        content: `确定要举报该用户\"${reason}\"吗？我们会认真处理您的举报。`,\n        confirmText: \"举报\",\n        confirmColor: \"#ff4757\",\n        success: res => {\n          if (res.confirm) {\n            // 提交举报\n            this.submitReport(reason);\n          }\n        }\n      });\n    },\n\n    // 提交举报到服务器\n    async submitReport(reason) {\n      // 显示加载提示\n      uni.showLoading({\n        title: \"提交中...\"\n      });\n\n      try {\n        const reportData = {\n          reportedUserId: this.chatId,\n          reportedUserName: this.chatName,\n          reason: reason,\n          reportType: \"chat\",\n          description: `在聊天中举报用户：${reason}`,\n          timestamp: new Date().toISOString()\n        };\n\n        const result = await reportUser(reportData);\n\n        uni.hideLoading();\n\n        if (result && result.code === 0) {\n          uni.showToast({\n            title: \"举报已提交\",\n            icon: \"success\",\n            duration: 2000\n          });\n\n          // 记录举报成功\n          console.log(\"举报提交成功:\", result);\n        } else {\n          console.error(\"举报提交失败:\", result?.message || \"未知错误\");\n          uni.showToast({\n            title: result?.message || \"提交失败，请重试\",\n            icon: \"error\",\n            duration: 2000\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        console.error(\"举报提交失败:\", error);\n        uni.showToast({\n          title: \"提交失败，请重试\",\n          icon: \"error\",\n          duration: 2000\n        });\n      }\n    },\n\n    loadMoreMessages() {\n      // 加载更多历史消息\n    },\n\n    resendMessage(message) {\n      message.status = \"sending\";\n      setTimeout(() => {\n        message.status = \"sent\";\n      }, 500);\n    },\n\n    chooseLocation() {\n      this.showExtensions = false;\n      this.$u.toast(\"位置功能开发中\");\n    },\n\n    chooseFile() {\n      this.showExtensions = false;\n      this.$u.toast(\"文件功能开发中\");\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.chat-container {\n  height: 100vh;\n  background: #f0f0f0;\n  position: relative;\n}\n\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  background: #fff;\n  border-bottom: 2rpx solid #e4e7ed;\n  padding-top: var(--status-bar-height);\n}\n\n.header-content {\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.avatar-container {\n  position: relative;\n  margin-right: 24rpx;\n}\n\n.header-avatar {\n  cursor: pointer;\n}\n\n.chat-info {\n  cursor: pointer;\n  flex: 1;\n}\n\n.name-container {\n  position: relative;\n  display: inline-block;\n}\n\n.chat-name {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  padding-right: 20rpx;\n}\n\n.online-indicator {\n  position: absolute;\n  bottom: 2rpx;\n  right: 2rpx;\n  width: 16rpx;\n  height: 16rpx;\n  background: #52c41a;\n  border-radius: 50%;\n  border: 2rpx solid #fff;\n  box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  animation: online-pulse 2s infinite;\n}\n\n@keyframes online-pulse {\n  0% {\n    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 0 6rpx rgba(82, 196, 26, 0.1);\n  }\n  100% {\n    box-shadow: 0 0 0 2rpx rgba(82, 196, 26, 0.2);\n  }\n}\n\n.header-actions {\n  display: flex;\n  gap: 32rpx;\n}\n\n.message-list {\n  position: fixed;\n  top: calc(88rpx + var(--status-bar-height) + 2rpx);\n  bottom: 120rpx;\n  left: 0;\n  right: 0;\n  padding: 32rpx;\n  overflow-y: auto;\n  width: auto;\n}\n\n.message-item {\n  margin-bottom: 32rpx;\n  margin-top: 32rpx;\n}\n\n.time-divider {\n  text-align: center;\n  margin-bottom: 32rpx;\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #999;\n  background: rgba(0, 0, 0, 0.1);\n  padding: 8rpx 24rpx;\n  border-radius: 24rpx;\n}\n\n.message-wrapper {\n  display: flex;\n  align-items: flex-end;\n}\n\n.message-wrapper.is-mine {\n  justify-content: flex-end;\n}\n\n.message-avatar {\n  margin: 0 16rpx;\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.message-avatar:hover {\n  transform: scale(1.05);\n}\n\n.message-content {\n  display: flex;\n  flex-direction: column;\n  width: auto;\n}\n\n/* 对方消息：内容左对齐 */\n.message-wrapper:not(.is-mine) .message-content {\n  align-items: flex-start;\n}\n\n/* 我的消息：内容右对齐 */\n.message-wrapper.is-mine .message-content {\n  align-items: flex-end;\n}\n\n.message-bubble {\n  padding: 24rpx 32rpx;\n  border-radius: 36rpx;\n  margin-bottom: 8rpx;\n}\n\n.text-bubble {\n  background: #fff;\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n}\n\n.text-bubble.mine {\n  background: #2979ff;\n}\n\n.message-text {\n  font-size: 32rpx;\n  line-height: 1.4;\n  color: #333;\n}\n\n.mine .message-text {\n  color: #fff;\n}\n\n.image-bubble {\n  padding: 0;\n  overflow: hidden;\n  background: transparent;\n}\n\n.message-image {\n  width: 300rpx;\n  height: 300rpx;\n  border-radius: 24rpx;\n}\n\n.voice-bubble {\n  background: #2979ff;\n  display: flex;\n  align-items: center;\n  min-width: 160rpx;\n  position: relative;\n}\n\n.voice-duration {\n  color: #fff;\n  font-size: 28rpx;\n  margin-left: 16rpx;\n}\n\n.voice-animation {\n  display: flex;\n  gap: 4rpx;\n  margin-left: 16rpx;\n}\n\n.wave {\n  width: 4rpx;\n  height: 24rpx;\n  background: #fff;\n  animation: wave 1s infinite;\n}\n\n.wave:nth-child(2) {\n  animation-delay: 0.1s;\n}\n\n.wave:nth-child(3) {\n  animation-delay: 0.2s;\n}\n\n@keyframes wave {\n  0%,\n  100% {\n    height: 8rpx;\n  }\n  50% {\n    height: 24rpx;\n  }\n}\n\n.message-status {\n  margin-top: 8rpx;\n}\n\n.input-area {\n  background: #fff;\n  border-top: 2rpx solid #e4e7ed;\n}\n\n.extensions-panel,\n.emoji-panel {\n  height: 400rpx;\n  border-bottom: 2rpx solid #e4e7ed;\n}\n\n.extension-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 40rpx;\n  gap: 40rpx;\n}\n\n.extension-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: calc(25% - 30rpx);\n}\n\n.extension-icon {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16rpx;\n}\n\n.extension-icon.photo {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.extension-icon.voice {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.extension-icon.location {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.extension-icon.file {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.extension-text {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.emoji-scroll {\n  height: 100%;\n  padding: 32rpx;\n}\n\n.emoji-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n}\n\n.emoji-item {\n  font-size: 48rpx;\n  padding: 16rpx;\n  text-align: center;\n  width: 80rpx;\n  height: 80rpx;\n  line-height: 48rpx;\n}\n\n.input-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  display: flex;\n  align-items: flex-end;\n  padding: 16rpx 24rpx;\n  gap: 16rpx;\n  min-height: 104rpx;\n  background: #fff;\n  border-top: 1rpx solid #e4e7ed;\n  /* 确保在键盘弹起时的兼容性 */\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n  box-sizing: border-box;\n  transition: all 0.3s ease;\n}\n\n.input-bar.input-focused {\n  border-top-color: rgba(41, 121, 255, 0.2);\n  background: #fafbfc;\n}\n\n.voice-btn {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 36rpx;\n  background: #f5f5f5;\n  transition: all 0.2s ease;\n}\n\n.voice-btn:active {\n  background: #e8e8e8;\n  transform: scale(0.95);\n}\n\n.input-wrapper {\n  flex: 1;\n  position: relative;\n  background: #f5f5f5;\n  border-radius: 24rpx;\n  padding: 16rpx 24rpx;\n  min-height: 72rpx;\n  max-height: 240rpx;\n  transition: all 0.3s ease;\n  border: 2rpx solid transparent;\n}\n\n.input-wrapper.has-text {\n  background: #f0f0f0;\n}\n\n.input-wrapper:focus-within {\n  background: #f0f0f0;\n  border-color: rgba(41, 121, 255, 0.3);\n  box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.1);\n}\n\n/* 原生textarea样式 */\n.input-textarea {\n  width: 100%;\n  font-size: 32rpx;\n  color: #333;\n  line-height: 1.5;\n  background: transparent;\n  border: none;\n  outline: none;\n  resize: none;\n  min-height: 40rpx;\n  max-height: 200rpx;\n  padding: 0;\n  box-sizing: border-box;\n  word-break: break-all;\n  white-space: pre-wrap;\n}\n\n.input-textarea::placeholder {\n  color: #999;\n  font-size: 32rpx;\n}\n\n/* 字数统计 */\n.char-count {\n  position: absolute;\n  bottom: 8rpx;\n  right: 16rpx;\n  font-size: 24rpx;\n  color: #999;\n  pointer-events: none;\n}\n\n.char-count .over-limit {\n  color: #ff4757;\n  font-weight: 500;\n}\n\n/* 防止输入时的布局跳动 */\n.input-wrapper {\n  will-change: height;\n  contain: layout style;\n}\n\n.voice-record-btn {\n  flex: 1;\n  height: 72rpx;\n  background: #f5f5f5;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  transition: all 0.3s ease;\n  border: 2rpx solid transparent;\n}\n\n.voice-record-btn.record-ready:active {\n  background: #e8e8e8;\n  transform: scale(0.98);\n}\n\n.voice-record-btn.recording {\n  background: linear-gradient(135deg, #ff4757, #ff3742);\n  border-color: #ff4757;\n  transform: scale(1.02);\n  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);\n}\n\n.record-icon {\n  margin-right: 8rpx;\n}\n\n.record-text {\n  font-size: 28rpx;\n  color: #666;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n\n.recording .record-text {\n  color: #fff;\n}\n\n/* 录音波形动画 */\n.record-wave {\n  position: absolute;\n  right: 16rpx;\n  display: flex;\n  gap: 4rpx;\n  align-items: center;\n}\n\n.wave-dot {\n  width: 6rpx;\n  height: 6rpx;\n  background: #fff;\n  border-radius: 50%;\n  animation: wave 1.4s ease-in-out infinite both;\n}\n\n.wave-dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.wave-dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n@keyframes wave {\n  0%,\n  80%,\n  100% {\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\n  40% {\n    transform: scale(1.2);\n    opacity: 1;\n  }\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.action-btn {\n  width: 72rpx;\n  height: 72rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 36rpx;\n  background: #f5f5f5;\n  transition: all 0.2s ease;\n}\n\n.action-btn:active {\n  background: #e8e8e8;\n  transform: scale(0.95);\n}\n\n.send-btn {\n  width: 72rpx;\n  height: 72rpx;\n  background: #2979ff;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n  transform-origin: center;\n}\n\n.send-btn.can-send {\n  background: #2979ff;\n  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.3);\n}\n\n.send-btn.can-send:active {\n  transform: scale(0.95);\n  background: #1e6bd8;\n}\n\n.send-btn.sending {\n  background: #999;\n  pointer-events: none;\n}\n\n.send-btn:not(.can-send) {\n  background: #ccc;\n  pointer-events: none;\n}\n</style>\n", "import mod from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7a033bbb&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753844359027\n      var cssReload = require(\"D:/developmentTool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}