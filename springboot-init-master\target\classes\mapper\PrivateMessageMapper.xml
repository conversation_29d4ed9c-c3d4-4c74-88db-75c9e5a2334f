<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yupi.springbootinit.mapper.PrivateMessageMapper">

    <resultMap id="MessageVOResultMap" type="com.yupi.springbootinit.model.vo.MessageVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="conversationId" column="conversation_id" jdbcType="BIGINT"/>
        <result property="senderId" column="sender_id" jdbcType="BIGINT"/>
        <result property="senderNickname" column="sender_nickname" jdbcType="VARCHAR"/>
        <result property="senderAvatar" column="sender_avatar" jdbcType="VARCHAR"/>
        <result property="receiverId" column="receiver_id" jdbcType="BIGINT"/>
        <result property="messageType" column="message_type" jdbcType="INTEGER"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="mediaUrl" column="media_url" jdbcType="VARCHAR"/>
        <result property="isRead" column="is_read" jdbcType="INTEGER"/>
        <result property="readTime" column="read_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询会话消息列表 -->
    <select id="selectMessagePage" resultMap="MessageVOResultMap">
        SELECT 
            pm.id, pm.conversation_id, pm.sender_id, pm.receiver_id,
            pm.message_type, pm.content, pm.media_url, pm.is_read,
            pm.read_time, pm.create_time,
            u.nickname as sender_nickname, u.avatar as sender_avatar
        FROM private_messages pm
        LEFT JOIN ba_user u ON pm.sender_id = u.id
        WHERE pm.conversation_id = #{conversationId} AND pm.is_delete = 0
        ORDER BY pm.create_time ASC
    </select>

    <!-- 查询会话的最后一条消息 -->
    <select id="selectLastMessage" resultType="com.yupi.springbootinit.model.entity.PrivateMessage">
        SELECT * FROM private_messages 
        WHERE conversation_id = #{conversationId} AND is_delete = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 标记会话中接收者的消息为已读 -->
    <update id="markMessagesAsRead">
        UPDATE private_messages 
        SET is_read = 1, read_time = NOW()
        WHERE conversation_id = #{conversationId} 
        AND receiver_id = #{receiverId} 
        AND is_read = 0 
        AND is_delete = 0
    </update>

    <!-- 统计会话中接收者的未读消息数 -->
    <select id="countUnreadMessages" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM private_messages 
        WHERE conversation_id = #{conversationId} 
        AND receiver_id = #{receiverId} 
        AND is_read = 0 
        AND is_delete = 0
    </select>

    <!-- 删除会话中的所有消息 -->
    <update id="deleteByConversationId">
        UPDATE private_messages 
        SET is_delete = 1
        WHERE conversation_id = #{conversationId}
    </update>

    <!-- 查询用户发送的消息列表 -->
    <select id="selectSentMessages" resultMap="MessageVOResultMap">
        SELECT 
            pm.id, pm.conversation_id, pm.sender_id, pm.receiver_id,
            pm.message_type, pm.content, pm.media_url, pm.is_read,
            pm.read_time, pm.create_time,
            u.nickname as sender_nickname, u.avatar as sender_avatar
        FROM private_messages pm
        LEFT JOIN ba_user u ON pm.sender_id = u.id
        WHERE pm.sender_id = #{senderId} AND pm.is_delete = 0
        ORDER BY pm.create_time DESC
    </select>

    <!-- 查询用户接收的消息列表 -->
    <select id="selectReceivedMessages" resultMap="MessageVOResultMap">
        SELECT 
            pm.id, pm.conversation_id, pm.sender_id, pm.receiver_id,
            pm.message_type, pm.content, pm.media_url, pm.is_read,
            pm.read_time, pm.create_time,
            u.nickname as sender_nickname, u.avatar as sender_avatar
        FROM private_messages pm
        LEFT JOIN ba_user u ON pm.sender_id = u.id
        WHERE pm.receiver_id = #{receiverId} AND pm.is_delete = 0
        ORDER BY pm.create_time DESC
    </select>

    <!-- 批量删除消息 -->
    <update id="batchDelete">
        UPDATE private_messages 
        SET is_delete = 1
        WHERE id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

</mapper>
