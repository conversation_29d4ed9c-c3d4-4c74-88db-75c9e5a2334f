<block wx:if="{{show}}"><view data-event-opts="{{[['touchmove',[['e0',['$event']]]]]}}" class="u-tabbar data-v-627f7c73" catchtouchmove="__e"><view class="{{['u-tabbar__content','safe-area-inset-bottom','data-v-627f7c73',(borderTop)?'u-border-top':'']}}" style="{{'height:'+($root.g0)+';'+('background-color:'+(bgColor)+';')}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['clickHandler',[index]]]]]}}" class="{{['u-tabbar__content__item','data-v-627f7c73',(midButton&&item.$orig.midButton)?'u-tabbar__content__circle':'']}}" style="{{'background-color:'+(bgColor)+';'}}" catchtap="__e"><view class="{{['data-v-627f7c73',midButton&&item.$orig.midButton?'u-tabbar__content__circle__button':'u-tabbar__content__item__button']}}"><u-icon vue-id="{{'251eda1a-1-'+index}}" size="{{midButton&&item.$orig.midButton?midButtonSize:iconSize}}" name="{{item.m0}}" img-mode="scaleToFill" color="{{item.m1}}" custom-prefix="{{item.$orig.customIcon?'custom-icon':'uicon'}}" class="data-v-627f7c73" bind:__l="__l"></u-icon><block wx:if="{{item.$orig.count||item.$orig.isDot}}"><u-badge vue-id="{{'251eda1a-2-'+index}}" count="{{item.$orig.count}}" is-dot="{{item.$orig.isDot}}" offset="{{[-2,item.m2]}}" class="data-v-627f7c73" bind:__l="__l"></u-badge></block></view><view class="u-tabbar__content__item__text data-v-627f7c73" style="{{'color:'+(item.m3)+';'}}"><text class="u-line-1 data-v-627f7c73">{{item.$orig.text}}</text></view></view></block><block wx:if="{{midButton}}"><view class="{{['u-tabbar__content__circle__border','data-v-627f7c73',(borderTop)?'u-border':'']}}" style="{{'background-color:'+(bgColor)+';'+('left:'+(midButtonLeft)+';')}}"></view></block></view><view class="u-fixed-placeholder safe-area-inset-bottom data-v-627f7c73" style="{{'height:'+('calc('+$root.g1+' + '+(midButton?48:0)+'rpx)')+';'}}"></view></view></block>